﻿@page "/manageprofile"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "manageprofileAccessPolicy")]
@layout Admin
@using System.Text.Json
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel
@using TeyaWebApp.Components.Pages
@inject ITokenService TokenService
@inject GraphApiService AuthService
@inject IDialogService DialogService
@using TeyaWebApp.TeyaAIScribeResource
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider

@using MudBlazor

<PageTitle>Profile Settings</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="py-8">
    <MudForm @ref="form" @bind-IsValid="isFormValid">
        <MudGrid Spacing="4">
        <!-- Personal Information Section - Now full width -->
        <MudItem xs="12">
            <MudPaper Class="pa-6" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">Personal Information</MudText>

                <MudGrid Spacing="4">
                    <!-- First Row: Username and Email -->
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_UserName"
                                      Label="Username"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Person" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_Email"
                                      Label="Email"
                                      ReadOnly="true"
                                     
                                      Variant="Variant.Outlined"
                                      InputType="InputType.Email"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Email" />
                    </MudItem>

                    <!-- Second Row: First Name and Last Name -->
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_FirstName"
                                      Label="First Name"
                                      Validation="ValidateAlphabetic"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Badge" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_LastName"
                                      Label="Last Name"
                                      Validation="ValidateAlphabetic"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Badge" />
                    </MudItem>

                    <!-- Third Row: Role and Theme -->
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_Role"
                                      Label="Role"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                      
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Work" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_OrganizationName"
                                      Label="Organization Name"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                    
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.LocationCity" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudSelect T="ThemeItem"
                                   Label="Theme"
                                   Variant="Variant.Outlined"
                                   Class="mt-2"
                                   Value="SelectedTheme"
                                   ValueChanged="OnThemeChanged"
                                   Adornment="Adornment.Start"
                                   AdornmentIcon="@Icons.Material.Filled.Palette">
                            @foreach (var theme in Themes)
                            {
                                <MudSelectItem Value="@theme">@theme.Name</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>

        <!-- Address Information -->
        <MudItem xs="12">
            <MudPaper Class="pa-6" Elevation="3">
                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">Address Information</MudText>

                <MudGrid Spacing="4">
                    <!-- Address Lines -->
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_AddressLine1"
                                      Label="Address Line 1"
                                      Required="true"
                                      Validation="ValidateAddress"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Home" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="User_AddressLine2"
                                      Label="Address Line 2 (Optional)"
                                      Validation="ValidateAddress"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Apartment" />
                    </MudItem>

                    <!-- City, State, Postal Code -->
                    <MudItem xs="12" md="4">
                        <MudTextField @bind-Value="User_City"
                                      Label="City"
                                          Required="true"
                                      Validation="ValidateAlphabetic"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.LocationCity" />
                    </MudItem>

                    <MudItem xs="12" md="4">
                        <MudTextField @bind-Value="User_State"
                                      Label="State/Province"
                                          Required="true"
                                      Validation="ValidateAlphabetic"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Map" />
                    </MudItem>

                    <MudItem xs="12" md="4">
                        <MudTextField @bind-Value="User_PostalCode"
                                      Label="Postal Code"
                                          Required="true"
                                      Validation="@ValidatePostalCode"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.LocalPostOffice" />
                    </MudItem>

                    <!-- Country -->
                    <MudItem xs="12">
                            <MudAutocomplete T="string"
                                             Required="true"
                                             Label="@Localizer["Country"]"
                                             Value="@User_Country"
                                             ValueChanged="@(value => User_Country = value)"
                                             SearchFunc="SearchCountries"
                                             Clearable="true"
                                             Variant="Variant.Outlined"
                                             Class="modern-autocomplete"
                                             Placeholder="Search Country" />
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>

        <!-- Action Buttons -->
        <MudItem xs="12">
            <MudItem xs="12" Class="d-flex justify-end gap-2">
                <MudButton Variant="Variant.Outlined"
                           OnClick="Cancel"
                           Color="Color.Secondary"
                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;"
                           Class="uniform-button">
                    Cancel
                </MudButton>
                <MudButton Variant="Variant.Filled"
                           OnClick="Save"
                           Color="Color.Primary"
                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;"
                           Class="uniform-button">
                    Save
                </MudButton>
            </MudItem>

        </MudItem>
    </MudGrid>
   </MudForm>
</MudContainer>

<style>
    .selected-theme {
        border: 2px solid var(--mud-palette-primary);
        background-color: rgba(var(--mud-palette-primary-rgb), 0.1);
    }

    .cursor-pointer {
        cursor: pointer;
        transition: all 0.2s ease;
    }

        .cursor-pointer:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
</style>

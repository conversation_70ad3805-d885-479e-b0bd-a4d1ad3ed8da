﻿@page "/config"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "configAccessPolicy")]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Buttons
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@inject IDialogService DialogService

<MudPaper Elevation="1" Class="mb-3 custom-tabs">
	<MudTabs @bind-ActivePanelIndex="activeTabIndex" Elevation="0" Rounded="false" ApplyEffectsToContainer="false" PanelClass="pa-0" Class="modern-tab-container">

		<MudTabPanel Text="Organization" Icon="@Icons.Material.Filled.CorporateFare" Class="@GetTabClass(0)">
			<GenericCard Heading="@Localizer["Organization"]">
				<MudGrid Spacing="3">
					<MudItem xs="12" lg="5">
						<MudPaper Elevation="2" Class="p-4">
							<MudGrid Spacing="2">
								<MudItem xs="12">
									<MudTextField Label="Organization Name"
												  @bind-Value="Organization.OrganizationName"
												  ReadOnly="true" />
								</MudItem>
								<MudItem xs="12">
									<MudAutocomplete T="string"
													 Required="true"
													 Label="@Localizer["Country"]"
													 Value="@Organization.Country"
													 ValueChanged="@(value => Organization.Country = value)"
													 SearchFunc="SearchCountries"
													 Clearable="true"
													 Placeholder="Search Country" />
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="Address" @bind-Value="Organization.Address" />
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="Contact Number"
												  @bind-Value="Organization.ContactNumber"
												  MaxLength="12"
												  InputType="MudBlazor.InputType.Telephone"
												  Immediate="true"
												  OnBlur="@ValidateContactNumber"
												  Error="@HasPhoneNumberError"
												  ErrorText="@PhoneNumberErrorText" />
								</MudItem>

								<MudItem xs="12">
									<MudTextField Label="Email" @bind-Value="Organization.Email" InputType="MudBlazor.InputType.Email" />
								</MudItem>
							</MudGrid>
							<MudGrid Spacing="2" Class="mt-4">
								<MudItem xs="12" Class="d-flex justify-end gap-2">
									<MudButton Color="Color.Secondary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Outlined" Class="uniform-button" OnClick="CancelOrganizationForm">Cancel</MudButton>
									<MudButton Color="Color.Primary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Filled" Class="uniform-button" OnClick="SaveOrganizationForm">Save</MudButton>
								</MudItem>
							</MudGrid>
						</MudPaper>
					</MudItem>
				</MudGrid>
			</GenericCard>
		</MudTabPanel>

		<MudTabPanel Text="Facilities" Icon="@Icons.Material.Filled.LocationOn" Class="@GetTabClass(1)">
			<GenericCard Heading="@Localizer["Facilities"]">
				<MudGrid Spacing="3">
					<MudItem xs="12" lg="5">
						<MudPaper Elevation="2" Class="p-4">
							<MudGrid Spacing="2">
								<MudItem xs="12">
									<MudTextField Label="Facility Name" @bind-Value="Facility.FacilityName" @ref="facilityNameInput" />
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="Street Name" @bind-Value="Facility.StreetName"/>
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="City" @bind-Value="Facility.City"/>
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="Zip Code" @bind-Value="Facility.Zipcode"/>
								</MudItem>
								<MudItem xs="12">
									<MudTextField Label="State" @bind-Value="Facility.State"/>
								</MudItem>
								<MudItem xs="12">
									<MudAutocomplete T="string"
													 Required="true"
													 Label="@Localizer["Country"]"
													 Value="@Facility.Country"
													 ValueChanged="@(value => Facility.Country = value)"
													 SearchFunc="SearchCountries"
													 Clearable="true"
													 Placeholder="Search Country" />
								</MudItem>
							</MudGrid>
							<MudGrid Spacing="2" Class="mt-4">
								<MudItem xs="12" Class="d-flex justify-end gap-2">
									<MudButton Color="Color.Secondary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Outlined" Class="uniform-button" OnClick="CancelFacilityForm">Cancel</MudButton>
									<MudButton Color="Color.Primary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Filled" Class="uniform-button" OnClick="SaveFacilityAsync">Add</MudButton>
								</MudItem>
							</MudGrid>
						</MudPaper>
					</MudItem>

					<MudItem xs="12" lg="7">
						<MudPaper Elevation="2" Class="p-2">
							<SfGrid DataSource="@ActiveUserFacilities" AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" @ref="facilityGrid">
								<GridPageSettings PageSize="10"></GridPageSettings>
								<GridColumns>
									<GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
									<GridColumn Field="@nameof(Facility.FacilityName)" HeaderText="Facility Name" TextAlign="TextAlign.Center"></GridColumn>
									<GridColumn Field="@nameof(Facility.CreatedDate)" HeaderText="Created Date" TextAlign="TextAlign.Center" Format="d" Type="ColumnType.Date"></GridColumn>
								</GridColumns>
							</SfGrid>
							<MudGrid Class="mt-0">
								<MudItem xs="12" Class="d-flex justify-end">
									<MudButton Variant="Variant.Filled"
											   Color="Color.Error"
											   Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;"
											   Class="uniform-button"
											   OnClick="ShowDeleteConfirmationForFacility">
										Delete
									</MudButton>
								</MudItem>
							</MudGrid>
						</MudPaper>
					</MudItem>
				</MudGrid>
			</GenericCard>
		</MudTabPanel>

		<MudTabPanel Text="Roles" Icon="@Icons.Material.Filled.Person" Class="@GetTabClass(2)">
			<GenericCard Heading="@Localizer["Role"]">
				<MudGrid Spacing="3">
					<MudItem xs="12" lg="5">
						<MudPaper Elevation="2" Class="p-4">
							<MudGrid Spacing="2">
								<MudItem xs="12">
									<MudTextField Label="Role Name" @bind-Value="Role.RoleName" @ref="roleNameInput" Clearable="true" />
								</MudItem>
							</MudGrid>
							<MudGrid Spacing="2" Class="mt-4">
								<MudItem xs="12" Class="d-flex justify-end gap-2">
									<MudButton Color="Color.Primary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Filled" Class="uniform-button" OnClick="SaveRoleAsync">Add</MudButton>
								</MudItem>
							</MudGrid>
						</MudPaper>
					</MudItem>
					<MudItem xs="12" lg="7">
						<MudPaper Elevation="2" Class="p-2">
							<SfGrid DataSource="@ActiveRoles" AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" @ref="roleGrid">
								<GridPageSettings PageSize="10"></GridPageSettings>
								<GridColumns>
									<GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
									<GridColumn Field="@nameof(Role.RoleName)" HeaderText="Role Name" TextAlign="TextAlign.Left"></GridColumn>
									<GridColumn Field="@nameof(Role.CreatedDate)" HeaderText="Role Created Date" TextAlign="TextAlign.Center" Format="d" Type="ColumnType.Date"></GridColumn>
								</GridColumns>
							</SfGrid>
							<MudGrid Class="mt-0">
								<MudItem xs="12" Class="d-flex justify-end">
									<MudButton Variant="Variant.Filled"
											   Color="Color.Error"
											   Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;"
											   Class="uniform-button"
											   OnClick="ShowDeleteConfirmationForRole">
										Delete
									</MudButton>
								</MudItem>
							</MudGrid>
						</MudPaper>
					</MudItem>
				</MudGrid>
			</GenericCard>
		</MudTabPanel>

		<MudTabPanel Text="Permissions" Icon="@Icons.Material.Filled.Security" Class="@GetTabClass(3)">
			<GenericCard Heading="@Localizer["Permissions"]">
				<!-- Role Selection outside -->
				<MudGrid Spacing="3" Class="mb-4">
					<MudItem xs="12" md="6" lg="3">
						<MudSelect Label="@Localizer["Role"]" @bind-Value="selectedRole" Required="true">
							@foreach (var role in NonAdminRoles)
							{
								<MudSelectItem Value="@role">@role.RoleName</MudSelectItem>
							}
						</MudSelect>
					</MudItem>
				</MudGrid>
				@if (ShouldShowPageRoleMappingGrid)
				{
					<MudPaper Elevation="2" Class="p-3">
						<SfGrid DataSource="@PageUrls" AllowPaging="true" AllowSorting="true" @ref="pageRoleMappingGrid" CssClass="compact-grid">
							<GridPageSettings PageSize="20"></GridPageSettings>
							<GridColumns>
								<GridColumn Field="PagePath" HeaderText="@Localizer["Page URL"]" TextAlign="TextAlign.Left" Width="50%" />
								<GridColumn HeaderText="@Localizer["Access"]" TextAlign="TextAlign.Left" Width="50%">
									<HeaderTemplate>
										<div style="display: flex; align-items: center; gap: 8px;">
											<div class="access-checkbox" style="margin-left: 3px;">
												<SfCheckBox @bind-Checked="selectAllChecked"
															@onchange="OnSelectAllChanged" />
											</div>
											<span>@Localizer["Access"]</span>
										</div>
									</HeaderTemplate>
									<Template>
										<div class="access-checkbox">
											<SfCheckBox @bind-Checked="((PageRoleMappingData)context).HasAccess"
														@onchange="(e) => OnCheckboxChanged((PageRoleMappingData)context)" />
										</div>
									</Template>
								</GridColumn>
							</GridColumns>
						</SfGrid>
						<MudGrid Class="mt-4" Spacing="2">
							<MudItem xs="12" Class="d-flex justify-end gap-2">
								<MudButton Color="Color.Secondary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Outlined" Class="uniform-button" OnClick="CancelChanges">
									@Localizer["Cancel"]
								</MudButton>
								<MudButton Color="Color.Primary" Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 90px;" Variant="Variant.Filled" Class="uniform-button" OnClick="SaveSelectedPageRoleMappings">
									@Localizer["Save"]
								</MudButton>
							</MudItem>
						</MudGrid>
					</MudPaper>
				}
				else
				{
					<MudAlert Severity="Severity.Info">
						@Localizer["Please select a role to manage permissions."]
					</MudAlert>
				}
			</GenericCard>
		</MudTabPanel>

		<MudTabPanel Text="Visit Type" Icon="@Icons.Material.Filled.EventNote" Class="@GetTabClass(4)">
			<GenericCard Heading="@Localizer["Visit Type"]">
				<MudGrid>
					<MudItem xs="12">
						<SfGrid @ref="visitTypeGrid"
								DataSource="@VisitTypes"
								TValue="VisitType"
								AllowPaging="true"
								AllowSorting="true"
								GridLines="GridLine.Both"
								Toolbar="@(new List<string>() { @Localizer["Add"] })">
							<GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" AllowEditOnDblClick="true"></GridEditSettings>
							<GridPageSettings PageSize="10"></GridPageSettings>
							<GridEvents OnActionComplete="OnGridAction" OnActionBegin="OnActionBegin" TValue="VisitType"></GridEvents>
							<GridColumns>
								<GridColumn Field="ID" IsPrimaryKey="true" Visible="false"></GridColumn>
								<GridColumn Field=@Localizer["VisitName"] HeaderText="Visit Name"
											TextAlign="TextAlign.Left" AllowEditing="false">
								</GridColumn>
								<GridColumn Field=@Localizer["CPTCode"] HeaderText="CPT Code"
											TextAlign="TextAlign.Left" AllowEditing="true">
								</GridColumn>
								<GridColumn HeaderText="@Localizer["Actions"]"
											TextAlign="TextAlign.Center" Width="100">
									<GridCommandColumns>
										<GridCommandColumn Type="CommandButtonType.Delete"
														   ButtonOption="@(new CommandButtonOptions() {
														   IconCss = "e-icons e-delete",
														   CssClass = "e-flat" })" />
									</GridCommandColumns>
								</GridColumn>
							</GridColumns>
						</SfGrid>
					</MudItem>
				</MudGrid>
			</GenericCard>
		</MudTabPanel>
	</MudTabs>
</MudPaper>

<style>
	/* Modern Tab Container */
	.custom-tabs {
		background: white;
		border-radius: 8px;
		box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
		overflow: hidden;
	}

	.modern-tab-container {
		width: 100%;
	}

	/* Tab Header Styling */
	.custom-tabs .mud-tabs-toolbar {
		background: white;
		border-bottom: 1px solid #e5e7eb;
		padding: 0;
		min-height: 0;
	}

	/* Individual Tab Styling */
	.custom-tabs .mud-tab {
		color: #6b7280 !important;
		background: white !important;
		border-bottom: 2px solid transparent !important;
		transition: all 0.2s ease-in-out !important;
		padding: 16px 24px !important;
		border-radius: 0 !important;
		min-height: auto !important;
		text-transform: none !important;
		font-weight: 550 !important;
		font-size: 15px !important;
		position: relative;
	}

		/* Tab Hover State */
		.custom-tabs .mud-tab:hover {
			color: #2563eb !important;
			background-color: #eff6ff !important;
			border-bottom-color: #bfdbfe !important;
		}

		/* Active Tab State */
		.custom-tabs .mud-tab.mud-tab-active {
			color: #2563eb !important;
			background-color: #eff6ff !important;
			border-bottom-color: #2563eb !important;
		}

	/* Tab Content Flex Layout */
	.tab-content-flex {
		display: flex;
		align-items: center;
		gap: 12px;
	}

	/* Tab Icon Styling */
	.tab-icon {
		transition: color 0.2s ease-in-out;
		color: inherit !important;
	}

	/* Tab Text Styling */
	.tab-text {
		font-weight: 550;
		font-size: 15px;
		color: inherit;
		white-space: nowrap;
	}

	/* Remove default MudBlazor tab ripple effect */
	.custom-tabs .mud-tab .mud-ripple {
		display: none !important;
	}

	/* Tab Panel Content - removed extra padding */
	.custom-tabs .mud-tabs-panels .mud-tab-panel {
		width: 100%;
		padding: 0 !important;
	}

	/* Override any default MudBlazor tab panel padding inconsistencies */
	.custom-tabs .mud-tab-panel-text {
		width: 100% !important;
		margin: 0 !important;
		padding: 0 !important;
	}

	/* Focus states for accessibility */
	.custom-tabs .mud-tab:focus {
		outline: 2px solid #2563eb;
		outline-offset: -2px;
	}

	/* Responsive adjustments */
	@@media (max-width: 768px) {
		.custom-tabs .mud-tab {
			padding: 12px 16px !important;
			font-size: 14px !important;
		}

		.tab-content-flex {
			gap: 8px;
		}

		.tab-text {
			font-size: 14px;
		}
	}
</style>

@code {
	private int activeTabIndex = 0;

	private string GetTabClass(int tabIndex) => activeTabIndex == tabIndex ? "tab-active" : string.Empty;
}
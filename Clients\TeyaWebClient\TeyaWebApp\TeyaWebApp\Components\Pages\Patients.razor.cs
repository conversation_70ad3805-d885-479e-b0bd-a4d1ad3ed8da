﻿using System.Text.RegularExpressions;
using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Navigations;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Templates;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using Unity;

public static class ImageTypeExtensions
{
    public static string GetMimeType(this AllowedImageType imageType, IStringLocalizer localizer)
    {
        return imageType switch
        {
            AllowedImageType.Jpeg => localizer["image/jpeg"],
            AllowedImageType.Jpg => localizer["image/jpg"],
            AllowedImageType.Png => localizer["image/png"],
            AllowedImageType.Gif => localizer["image/gif"],
            AllowedImageType.Bmp => localizer["image/bmp"],
            AllowedImageType.Webp => localizer["image/webp"],
            AllowedImageType.Tiff => localizer["image/tiff"],
            _ => throw new ArgumentOutOfRangeException(nameof(imageType), localizer["Unsupported image type."])
        };
    }

    public static IEnumerable<string> GetAllMimeTypes(IStringLocalizer localizer)
    {
        return Enum.GetValues<AllowedImageType>().Select(type => type.GetMimeType(localizer));
    }
}
public enum AllowedImageType
{
    Jpeg,
    Jpg,
    Png,
    Gif,
    Bmp,
    Webp,
    Tiff
}

namespace TeyaWebApp.Components.Pages
{
    public partial class Patients
    {
        private Member member = new Member();
        private Address address = new Address();
        private Insurance insurance = new Insurance();
        private Guardian guardian = new Guardian();
        private Employer employer = new Employer();
        private bool showMemberForm = true;
        private bool showGuardianForm = false;
        private bool isAddingNewMember = false;
        private bool showEmployerForm = false;
        private bool showStatsForm = false;
        private bool showMiscellaneousForm = false;
        private bool showAddressForm = false;
        private bool photoUploadSuccess = false;
        private string photoUploadMessage = "";
        private string uploadedFileName = string.Empty;

        [Parameter]
        public Guid? memberId { get; set; }
        private const int DefaultPasswordLength = 12;

        [Inject]
        private ILogger<Patients> Logger { get; set; }
        [Inject] private ICommunicationService CommunicationService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] UserContext UserContext { get; set; }
        private IBrowserFile selectedFile;
        private string signInType, issuer;
        [Inject] private GraphApiService _GraphApiService { get; set; } = default!;
        [Inject] private ActiveUser User { get; set; }
        [Inject] NavigationManager NavigationManager { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private ICountryService countryService { get; set; }

        private bool Subscription = false;

        // Method using the now properly-defined extension methods

        private string? imagePreviewUrl;
        private bool showPreview = false;

        private MudDatePicker? dobDatePicker;
        private MudDatePicker? financialReviewDatePicker;
        private MudDatePicker? effectiveDatePicker;
        private MudDatePicker? deceasedDatePicker;

        private bool ssnExists=false;
        private string ssnErrorText=string.Empty;
        // Stepper properties
        private SfStepper PatientStepper { get; set; }
        private StepperStep PersonalInfoStep { get; set; }
        private StepperStep GuardianStep { get; set; }
        private StepperStep EmployerStep { get; set; }
        private StepperStep DemographicsStep { get; set; }
        private StepperStep InsuranceStep { get; set; }
        private StepperStep AddressStep { get; set; }
        private StepperStep MiscStep { get; set; }

        // Step container classes
        private string PersonalInfoContainerClass { get; set; } = "step-active";
        private string GuardianContainerClass { get; set; } = "";
        private string EmploymentContainerClass { get; set; } = "";
        private string DemographicsContainerClass { get; set; } = "";
        private string InsuranceContainerClass { get; set; } = "";
        private string AddressContainerClass { get; set; } = "";
        private string MiscellaneousContainerClass { get; set; } = "";

        private bool IsNavigatingBackwards { get; set; } = false;
        private bool IsCurrentStepValid { get; set; } = false;

        private List<Member> allmember { get; set; }
        private async Task HandleFileUpload(IBrowserFile file, IStringLocalizer localizer)
        {
            uploadedFileName = file.Name;
            try
            {
                if (file != null)
                {

                    var allowedImageTypes = ImageTypeExtensions.GetAllMimeTypes(localizer);

                    if (!allowedImageTypes.Contains(file.ContentType.ToLower()))
                    {
                        photoUploadSuccess = false;
                        photoUploadMessage = $"Only image files are allowed ({string.Join(", ", Enum.GetNames<AllowedImageType>())}).";
                        Snackbar.Add(Localizer["Invalidfiletype"], Severity.Warning);
                        return;
                    }

                    if (file.Size > 10 * 1024 * 1024) // 10MB in bytes
                    {
                        photoUploadSuccess = false;
                        photoUploadMessage = Localizer["Imagefilesize"];
                        Snackbar.Add(Localizer["Filetoolarge"], Severity.Warning);
                        return;
                    }

                    // Generating preview
                    using (var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024))
                    using (var ms = new MemoryStream())
                    {
                        await stream.CopyToAsync(ms);
                        var base64 = Convert.ToBase64String(ms.ToArray());
                        imagePreviewUrl = $"data:{file.ContentType};base64,{base64}";
                    }
                    showPreview = false;

                    var azureBlobService = new AzureBlobService();
                    string imageUrl = await azureBlobService.UploadImageAsync(file);
                    member.ProfileImageUrl = imageUrl;
                    photoUploadSuccess = true;
                    photoUploadMessage = "Image added successfully!";
                    Snackbar.Add(Localizer["Image uploaded successfully"], Severity.Success);
                    await Task.Delay(5000);
                    photoUploadSuccess = false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error uploading Image");
                photoUploadSuccess = false;
                photoUploadMessage = $"Error uploading Image: {ex.Message}";
                Snackbar.Add(Localizer["FailedImage"], Severity.Error);
            }
        }

        private void TogglePreview()
        {
            showPreview = !showPreview;
        }


        private Task HandleFileUploadWrapper(IBrowserFile file)
        {
            return HandleFileUpload(file, Localizer);
        }

        private async Task RemovePatientPhoto()
        {
            try
            {
                member.ProfileImageUrl = string.Empty;
                uploadedFileName = string.Empty;
                Snackbar.Add(Localizer["PatientImagesuccessful"], Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer[$"FailedImages"], Severity.Error);
            }
        }
        private void ClearPhotoUploadMessage()
        {
            photoUploadSuccess = false;
            photoUploadMessage = "";
        }
        protected override void OnInitialized()
        {
            signInType = Localizer["emailAddress"];
            issuer = Environment.GetEnvironmentVariable("PrimaryDomain") ?? string.Empty;
        }
        private Guid ogid = Guid.Empty;
        protected override async Task OnParametersSetAsync()
        {
            var orgId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            if (memberId.HasValue)
            {

                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(orgId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                member = await MemberService.GetMemberByIdAsync(memberId.Value, orgId, Subscription);
                isAddingNewMember = false;


            }
            else
            {
                isAddingNewMember = true;
                member = new Member();
            }
            allmember = await MemberService.GetAllMembersAsync(orgId, Subscription);
        }
        private void ToggleMiscellaneousForm()
        {
            showMiscellaneousForm = !showMiscellaneousForm;
        }
        private void ToggleStatsForm()
        {
            showStatsForm = !showStatsForm;
        }

        private bool showInsuranceForm = false;

        private void ToggleInsuranceForm()
        {
            showInsuranceForm = !showInsuranceForm;
        }
        private void ToggleAddressForm()
        {
            showAddressForm = !showAddressForm;
        }

        private void ToggleMemberForm()
        {
            showMemberForm = !showMemberForm;
        }

        private void ToggleGuardianForm()
        {
            showGuardianForm = !showGuardianForm;
        }

        private void ToggleEmployerForm()
        {
            showEmployerForm = !showEmployerForm;
        }
        private async Task HandleSubmit()
        {
            try
            {
                
                // Validate all required fields before submission
                if (!ValidateAllSteps())
                {
                    return;
                }
               


                string dialogTitle = isAddingNewMember ?
                    Localizer["ConfirmPatientCreation"] :
                    Localizer["ConfirmUpdate"];

                string dialogMessage = isAddingNewMember ?
                    Localizer["Patient.Create"] :
                    Localizer["Patient.Update"];

                bool? result = await DialogService.ShowMessageBox(
                    dialogTitle,
                    dialogMessage,
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                if (result != true)
                {
                    return;
                }

                if (member.Id == Guid.Empty)
                {
                    try
                    {
                        string password = GenerateRandomPassword();
                        string role = "Patient";
                        string id = await _GraphApiService.CreateUserAsync2(member.UserName, member.Email, password, signInType, issuer, member.Email, role);

                        if (Guid.TryParse(id, out Guid parsedGuid))
                        {
                            member.Id = parsedGuid;
                        }
                        var recipientList = new List<string> { member.Email };
                        var response = await RenderComponentHtml(member.Email, password);
                        var sendMail = await CommunicationService.SendHtmlEmailService(
                            Localizer["EmailSenderAddress"], Localizer["EmailSubject"], response, recipientList);

                        if (sendMail.HasCompleted)
                        {
                            Logger.LogInformation(Localizer["EmailCreatedSuccessful"]);
                        }
                        else
                        {
                            Logger.LogWarning(Localizer["EmailPendingOrFailedMessage"]);
                        }
                    }
                    catch
                    {
                        throw new Exception(Localizer["FailedAddingUser"]);
                    }
                }

                if (isAddingNewMember)
                {
                    member.IsActive = true;        //should be fixed 
                    insurance.InsuranceId = Guid.NewGuid();
                    insurance.OrganizationId = UserContext.ActiveUserOrganizationID;

                    var insuranceAdded = await InsuranceService.AddInsuranceAsync(insurance);
                    if (!insuranceAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                        return;
                    }

                    address.AddressId = Guid.NewGuid();
                    address.OrganizationID = UserContext.ActiveUserOrganizationID;

                    var addressAdded = await AddressService.AddAddressAsync(address);
                    if (!addressAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                        return;
                    }

                    guardian.GuardianId = Guid.NewGuid();
                    guardian.OrganizationId = UserContext.ActiveUserOrganizationID;

                    var GuardianAdded = await GuardianService.AddGuardianAsync(guardian);
                    if (!GuardianAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                        return;
                    }

                    employer.EmployerId = Guid.NewGuid();
                    employer.OrganizationId = UserContext.ActiveUserOrganizationID;

                    var employerAdded = await EmployerService.AddEmployerAsync(employer);
                    if (!employerAdded)
                    {
                        Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                        return;
                    }
                    member.AddressId = address.AddressId;
                    member.InsuranceId = insurance.InsuranceId;
                    member.GuardianId = guardian.GuardianId;
                    member.EmployerId = employer.EmployerId;
                    member.RoleName = "Patient";
                    member.OrganizationName = User.OrganizationName;
                    member.OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                    var roles = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
                    roles = roles.Where(role => role.RoleName == "Patient").ToList();
                    member.RoleID = roles.FirstOrDefault()?.RoleId
                         ?? throw new Exception(Localizer["RoleNotFoundError"]);
                    var updateFields = new Dictionary<string, object>
                        {
                            { $"{Environment.GetEnvironmentVariable("EXTENSION-PREFIX")}_OrganizationName",member.OrganizationName},
                            { "Country",member.Country},
                            { "GivenName",member.FirstName },
                            { "SurName",member.LastName },
                        };
                    bool updateSuccessful = await _GraphApiService.UpdateUserProfileAsync(member.Id.ToString(), updateFields);
                    if (!updateSuccessful)
                    {
                        Logger.LogError(Localizer["FailedToUpdateDisplayName"]);
                    }

                    var registeredMember = await MemberService.RegisterMembersAsync(new List<Member> { member });
                    Logger.LogInformation(Localizer["MemberRegistered"], registeredMember.UserName);

                    Snackbar.Add(Localizer["Patient created successfully"], Severity.Success);
                    

                }
                else
                {
                    await MemberService.UpdateMemberByIdAsync(member.Id, member);
                    Logger.LogInformation(Localizer["MemberUpdated"], member.UserName);
                    Snackbar.Add(Localizer["Patient Details updated successfully"], Severity.Success);
                    NavigationManager.NavigateTo("/patients");
                }
                ResetForm();
                await ResetStepper();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingMemberData"]);
                Snackbar.Add(Localizer["Error processing request. Please try again."], Severity.Error);
            }
        }

        public async Task<string> RenderComponentHtml(string email, string password)
        {
            try
            {
                inviteMailParametersService.Email = email;
                inviteMailParametersService.Password = password;
                string renderedHtml = await RazorRenderer.RenderComponentToHtmlAsync<InviteMail>();
                return renderedHtml;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["RenderError"]);
                return string.Empty;
            }
        }
        private string GenerateRandomPassword(int length = DefaultPasswordLength)
        {
            string chars = Localizer["RandomPasswordChars"];

            Random random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                                         .Select(charSet => charSet[random.Next(charSet.Length)]).ToArray());
        }


        private async Task ResetStepper()
        {
            activeStep = 0; 

            if (PatientStepper != null)
            {
                await PatientStepper.ResetAsync();
                PersonalInfoStep.IsValid = null;
                GuardianStep.IsValid = null;
                EmployerStep.IsValid = null;
                DemographicsStep.IsValid = null;
                InsuranceStep.IsValid = null;
                AddressStep.IsValid = null;
                MiscStep.IsValid = null;

                // Reset validation flags
                IsCurrentStepValid = false;

                // Reset error states
                ssnExists = false;
                ssnErrorText = string.Empty;
                emailExists = false;
                emailErrorText = string.Empty;

                // Update active step classes to show first step as active
                UpdateActiveStepClasses(0);
                UpdateActiveStepClasses(activeStep);
            }

            StateHasChanged();
        }



        private void ResetForm()
        {
            member = new Member();
            address = new Address();
            insurance = new Insurance();
            guardian = new Guardian();
            employer = new Employer();
            isAddingNewMember = true;
            showMemberForm = false;
            showGuardianForm = false;
            showEmployerForm = false;
            showStatsForm = false;
            showMiscellaneousForm = false;
            showAddressForm = false;
            showInsuranceForm = false;

            // Reset the photo
            imagePreviewUrl = null;
            uploadedFileName = string.Empty;
            photoUploadSuccess = false;
            photoUploadMessage = "";
            showPreview = false;

            // Reset validation states
            ssnExists = false;
            ssnErrorText = string.Empty;
            emailExists = false;
            emailErrorText = string.Empty;
            IsCurrentStepValid = false;
        }

        private async Task HandleDelete()
        {
            try
            {
                if (member.Id != Guid.Empty)
                {
                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["ConfirmDeletion"],
                        Localizer["Confirm.Deletion"],
                        yesText: Localizer["Delete"],
                        noText: Localizer["Cancel"]);

                    if (result != true)
                    {
                        return;
                    }

                    var activeUserOrganizationId = member.OrganizationID ?? Guid.NewGuid();
                    await MemberService.DeleteMemberByIdAsync(member.Id, activeUserOrganizationId, Subscription);
                    Logger.LogInformation(Localizer["MemberDeleted"], member.UserName);
                    Snackbar.Add(Localizer["Patientdelete"], Severity.Success);
                    Navigation.NavigateTo(Localizer["PatientsPagePath"]);
                }
                ResetForm();
                await ResetStepper();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDeletingMember"]);
                Snackbar.Add(Localizer["Errordelete"], Severity.Error);
            }
            StateHasChanged();
        }

        /// <summary>
        /// Validates the Phone Number 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidatePhoneNumber(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = new string(value.Where(char.IsDigit).ToArray());

                if (digitsOnly.Length != 10)
                {
                    result = "Invalid phone number. Must be 10 digits (e.g., ************).";
                }
            }

            return result;
        }


        /// <summary>
        /// Validates the Postal Code 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidatePostalCode(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = value.Replace("-", "");

                if (!Regex.IsMatch(digitsOnly, @"^\d{5}(\d{4})?$"))
                {
                    result = Localizer["InvalidPostalCodeMessage"];
                }
                else if (digitsOnly.Length == 9)
                {

                    value = $"{digitsOnly.Substring(0, 5)}-{digitsOnly.Substring(5)}";

                }
                else if (digitsOnly.Length == 5)
                {
                    // Accept as-is 
                }
                else
                {
                    result = Localizer["InvalidPostalCodeFormat"];
                }
            }

            return result;
        }



        /// <summary>
        /// Validates the Alphabetic input only alphabetic characters and spaces are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAlphabetic(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' '))
                {
                    result = "Invalid input. Only alphabetic characters and spaces are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Alphanumeric input only alphanumeric characters and spaces are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAlphanumeric(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetterOrDigit(c) || c == ' '))
                {
                    result = "Invalid input. Only alphanumeric characters and spaces are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Decimal input only numbers and a decimal point are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateDecimal(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!decimal.TryParse(value, out _))
                {
                    result = "Invalid decimal input. Only numbers and a decimal point are allowed.";
                }
            }

            return result;
        }


        /// <summary>
        /// Validates the Numeric input only numbers are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateNumeric(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(char.IsDigit))
                {
                    result = "Invalid input. Only numbers are allowed.";
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the Address input only letters, numbers, spaces, commas, periods, and hyphens are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAddress(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!Regex.IsMatch(value, @"^[a-zA-Z0-9\s,.\-+/\\]+$"))
                {
                    result =Localizer["InvalidAddressMessage"];
                }
            }

            return result;
        }


        /// <summary>
        /// Fetch the country dropdown 
        /// </summary>
        private List<Country> _allCountries = new();

        private async Task<IEnumerable<string>> SearchCountries(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return new List<string>();


            if (_allCountries.Count == 0)
            {
                try
                {
                    _allCountries = (await countryService.GetAllCountriesAsync()).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching countries: {ex.Message}");
                    return new List<string>();
                }
            }

            return _allCountries
                .Where(c => c.CountryName.StartsWith(value, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.CountryName)
                .ToList();
        }
        /// <summary>
        /// Cross icon in the Date Picker
        /// </summary>

        // Shared close method
        private async Task CloseDatePicker(MudDatePicker picker)
        {
            if (picker is not null)
            {
                await picker.ClearAsync();
                await picker.CloseAsync();
            }
        }

        // Stepper methods
        private void HandleStepChange(StepperChangeEventArgs args)
        {
            IsNavigatingBackwards = args.ActiveStep < args.PreviousStep;
            if (!IsNavigatingBackwards)
            {
                ValidateCurrentStep(args);
            }
            else
            {
                IsCurrentStepValid = true;
            }
            if (IsCurrentStepValid)
            {
                UpdateActiveStepClasses(args.ActiveStep);
            }
        }

        private void ValidateCurrentStep(StepperChangeEventArgs args)
        {
            switch (PatientStepper.ActiveStep)
            {
                case 0:
                    IsCurrentStepValid = ValidatePersonalInfo();

                    // Check for duplicate email/SSN
                    if (emailExists)
                    {
                        Snackbar.Add("Email already exists.", Severity.Warning);
                        IsCurrentStepValid = false;
                    }

                    if (ssnExists)
                    {
                        Snackbar.Add("SSN already exists.", Severity.Warning);
                        IsCurrentStepValid = false;
                    }

                    PersonalInfoStep.IsValid = IsCurrentStepValid;
                    break;

                case 1:
                    IsCurrentStepValid = ValidateGuardianInfo();
                    GuardianStep.IsValid = IsCurrentStepValid;
                    break;

                case 2:
                    IsCurrentStepValid = ValidateEmploymentInfo();
                    EmployerStep.IsValid = IsCurrentStepValid;
                    break;

                case 3:
                    IsCurrentStepValid = ValidateDemographicsInfo();
                    DemographicsStep.IsValid = IsCurrentStepValid;
                    break;

                case 4:
                    IsCurrentStepValid = ValidateInsuranceInfo();
                    InsuranceStep.IsValid = IsCurrentStepValid;
                    break;

                case 5:
                    IsCurrentStepValid = ValidateAddressInfo();
                    AddressStep.IsValid = IsCurrentStepValid;
                    break;

                case 6:
                    IsCurrentStepValid = true;
                    MiscStep.IsValid = IsCurrentStepValid;
                    break;
            }

            // Cancel navigation if validation fails
            args.Cancel = !IsCurrentStepValid;
        }

        // Fix 4: Enhanced ValidatePersonalInfo method
        private bool ValidatePersonalInfo()
        {
          
            var isValid = !string.IsNullOrWhiteSpace(member.UserName) &&
                         !string.IsNullOrWhiteSpace(member.Email) &&
                         !string.IsNullOrWhiteSpace(member.FirstName) &&
                         !string.IsNullOrWhiteSpace(member.LastName) &&
                         !string.IsNullOrWhiteSpace(member.Country);

            
            if (!isValid)
            {
                Snackbar.Add(Localizer["Please fill all required fields in Personal Information"], Severity.Warning);
            }

            if (ssnExistsMember)
            {
                Snackbar.Add(" SSN already exists.", Severity.Error);
                return false;
            }


            return isValid;
        }

        private bool ValidateGuardianInfo()
        {
            // Guardian info is optional, so always return true
            if (ssnExistsGuardian )
            {
                
                return false;
            }

            if (emailExists)
            {
                
                return false;
            }

            return true;
        }

        private bool ValidateEmploymentInfo()
        {
            // Employment info is optional, so always return true
            return true;
        }

        private bool ValidateDemographicsInfo()
        {
            // Demographics info is optional, so always return true
            return true;
        }

        private bool ValidateInsuranceInfo()
        {
            // Insurance info is optional, so always return true
            return true;
        }

        private bool ValidateAddressInfo()
        {
            // Address info is optional, so always return true
            return true;
        }

        private bool ValidateAllSteps()
        {
            if (!ValidatePersonalInfo())
            {
                Snackbar.Add(Localizer["Please complete all required fields in Personal Information step"], Severity.Warning);
                return false;
            }

            return true;
        }

        private async Task NextStepp() => await PatientStepper.NextStepAsync();
        private async Task PreviousStepp() => await PatientStepper.PreviousStepAsync();


        private void UpdateActiveStepClasses(int activeStep)
        {
            // Reset all step classes first
            PersonalInfoContainerClass = "";
            GuardianContainerClass = "";
            EmploymentContainerClass = "";
            DemographicsContainerClass = "";
            InsuranceContainerClass = "";
            AddressContainerClass = "";
            MiscellaneousContainerClass = "";

            // Activate current step only
            switch (activeStep)
            {
                case 0: PersonalInfoContainerClass = "step-active"; break;
                case 1: GuardianContainerClass = "step-active"; break;
                case 2: EmploymentContainerClass = "step-active"; break;
                case 3: DemographicsContainerClass = "step-active"; break;
                case 4: InsuranceContainerClass = "step-active"; break;
                case 5: AddressContainerClass = "step-active"; break;
                case 6: MiscellaneousContainerClass = "step-active"; break;
            }
        }

        private int activeStep = 0;
        private MudForm personalForm;
        private MudForm guardianForm;
        private MudForm employmentForm;
        private MudForm demographicsForm;
        private MudForm insuranceForm;
        private MudForm addressForm;
        private MudForm miscellaneousForm;

        private async Task NextStep()
        {
            bool isValid = activeStep switch
            {
                0 => await ValidateForm(personalForm) && ValidatePersonalInfo(),
                1 => await ValidateForm(guardianForm) && ValidateGuardianInfo(),
                2 => await ValidateForm(employmentForm)&& ValidateEmploymentInfo(),
                3 => await ValidateForm(demographicsForm) && ValidateDemographicsInfo(),
                4 => await ValidateForm(insuranceForm)&& ValidateInsuranceInfo(),
                5 => await ValidateForm(addressForm)&& ValidateAddressInfo(),
                6 => await ValidateForm(miscellaneousForm),
                _ => true
            };



            if (!isValid)
            {
                Snackbar.Add("Please fix validation errors before continuing.", Severity.Error);
                return;
            }

            if (activeStep < 6)
            {
                await PatientStepper.NextStepAsync();
                activeStep++;
                UpdateActiveStepClasses(activeStep);
            }
        }


        private async Task PreviousStep()
        {
            bool isValid = activeStep switch
            {
                0 => await ValidateForm(personalForm) && ValidatePersonalInfo(),
                1 => await ValidateForm(guardianForm) && ValidateGuardianInfo(),
                2 => await ValidateForm(employmentForm) && ValidateEmploymentInfo(),
                3 => await ValidateForm(demographicsForm) && ValidateDemographicsInfo(),
                4 => await ValidateForm(insuranceForm) && ValidateInsuranceInfo(),
                5 => await ValidateForm(addressForm) && ValidateAddressInfo(),
                6 => await ValidateForm(miscellaneousForm),
                _ => true
            };


            if (!isValid)
            {
                Snackbar.Add("Please fix validation errors before going back.", Severity.Error);
                return;
            }
            if (activeStep > 0)
            {
                await PatientStepper.PreviousStepAsync(); // Stepper updates itself
                activeStep--;
                UpdateActiveStepClasses(activeStep);
            }
        }

        private async Task<bool> ValidateForm(MudForm form)
        {
            if (form == null)
                return true;

            await form.Validate();
            return form.IsValid;
        }




        /// <summary>
        /// Variable to show Error message in the SSN and Email 
        /// </summary>

        bool ssnExistsMember = false;
        string ssnErrorTextMember = string.Empty;
        bool ssnExistsGuardian = false;
        string ssnErrorTextGuardian = string.Empty;
        bool emailExists = false;
        string emailErrorText = string.Empty;

        /// <summary>
        /// Check Validate and formated the SSN 
        /// </summary>
        /// <returns></returns>
        private async Task ValidateAndCheckSSNAsync(string? ssn,Guid? orgId,Guid? currentId,Action<string> assignFormattedSsn,Action<bool, string> setValidationResult)
        {
            setValidationResult(false, string.Empty);

            if (string.IsNullOrWhiteSpace(ssn) || orgId == null)
                return;

            var digitsOnly = new string(ssn.Where(char.IsDigit).ToArray());

            if (digitsOnly.Length != 9)
            {
                setValidationResult(true, "SSN must be exactly 9 digits.");
                return;
            }

            if (digitsOnly == "000000000" ||
                digitsOnly.StartsWith("000") ||
                digitsOnly.Substring(3, 2) == "00" ||
                digitsOnly.Substring(5) == "0000")
            {
                setValidationResult(true, "Invalid SSN pattern.");
                return;
            }

            var formattedSSN = $"{digitsOnly[..3]}-{digitsOnly.Substring(3, 2)}-{digitsOnly[5..]}";
            assignFormattedSsn(formattedSSN);

            var exists = await DoesSSNExistAsync(formattedSSN, orgId.Value, false, currentId);
            if (exists)
            {
                setValidationResult(true, "SSN already exists.");
            }
        }


        public async Task<bool> DoesSSNExistAsync(string ssn, Guid? orgId, bool subscription, Guid? currentMemberId = null)
        {
            if (string.IsNullOrWhiteSpace(ssn) || orgId == null)
                return false;

            
            var allMembers = allmember;
            var normalizedSSN = ssn.Replace("-", "").Trim();

            return allMembers.Any(m =>
                m.Id != currentMemberId && 
                !string.IsNullOrWhiteSpace(m.SSN) &&
                m.SSN.Replace("-", "").Trim() == normalizedSSN);
        }


        private async Task OnMemberSSNBlur()
        {
            await ValidateAndCheckSSNAsync(
                member.SSN,
                ogid,
                member.Id,
                formatted => member.SSN = formatted,
                (hasError, errorText) =>
                {
                    ssnExistsMember = hasError;
                    ssnErrorTextMember = errorText;
                });
        }

        private async Task OnGuardianSSNBlur()
        {
            if (!string.IsNullOrWhiteSpace(guardian.GuardianSSIN) &&
                !string.IsNullOrWhiteSpace(member.SSN) &&
                new string(guardian.GuardianSSIN.Where(char.IsDigit).ToArray()) ==
                new string(member.SSN.Where(char.IsDigit).ToArray()))
                        {
                ssnExistsGuardian = true;
                ssnErrorTextGuardian = "Guardian SSN cannot be the same as the Member SSN.";
               
                return;
            }
            await ValidateAndCheckSSNAsync(
                guardian.GuardianSSIN,
                ogid,
                member.Id, // or guardian.Id
                formatted => guardian.GuardianSSIN = formatted,
                (hasError, errorText) =>
                {
                    ssnExistsGuardian = hasError;
                    ssnErrorTextGuardian = errorText;
                });
        }



        /// <summary>
        /// Check and Validate Email
        /// </summary>
        /// <returns></returns>

        private async Task ValidateAndCheckEmailAsync()
        {
            if (string.IsNullOrWhiteSpace(member.Email))
            {
                emailExists = false;
                emailErrorText = string.Empty;
                return;
            }

            var email = member.Email.Trim();

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
            }
            catch
            {
                emailExists = true;
                emailErrorText = "Invalid email format.";
                return;
            }

            var tldRegex = new Regex(@"^[^@\s]+@[^@\s]+\.(com|org|net|edu|gov|in|us|co|io|dev)$", RegexOptions.IgnoreCase);
            if (!tldRegex.IsMatch(email))
            {
                emailExists = true;
                emailErrorText = "Invalid email domain. Allowed: .com,.in,.us,.co,.io,.dev...";
                return;
            }

            
            emailExists = await DoesEmailExistAsync(email, member.Id);

            // Check if guardian email is the same as member email
            if (!string.IsNullOrWhiteSpace(email) &&
                !string.IsNullOrWhiteSpace(guardian.GuardianEmail) &&
                email.Equals(guardian.GuardianEmail.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                emailExists = true;
                emailErrorText = "Guardian email cannot be the same as the Member email.";
                Snackbar.Add(emailErrorText, Severity.Error);
                return;
            }
            emailErrorText = emailExists ? "Email already exists." : string.Empty;

         
            
        }

        /// <summary>
        /// Check the Email address is already exist or not
        /// </summary>
        /// <param name="email"></param>
        /// <param name="currentMemberId"></param>
        /// <returns></returns>
        public Task<bool> DoesEmailExistAsync(string email, Guid currentMemberId)
        {
            var exists = allmember.Any(m =>
                m.Id != currentMemberId &&
                !string.IsNullOrWhiteSpace(m.Email) &&
                m.Email.Trim().Equals(email.Trim(), StringComparison.OrdinalIgnoreCase));

            return Task.FromResult(exists);
        }
    }

}

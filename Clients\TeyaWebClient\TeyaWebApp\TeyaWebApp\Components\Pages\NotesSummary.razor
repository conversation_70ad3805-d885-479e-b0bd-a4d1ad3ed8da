﻿@page "/notessummary"
@using Microsoft.Extensions.Localization
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Charts
@using Syncfusion.Blazor
@using MudBlazor
@inject IVitalService VitalService
@inject IFamilyMemberService FamilyMemberService
@inject ICurrentMedicationService CurrentMedicationService
@inject IMedicalHistoryService MedicalHistoryService
@inject IAllergyService AllergyService

<div class="py-4">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
        <MudGrid>
            <MudItem xs="12"><MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Medical Summary"]</MudText></MudItem>
            <MudItem xs="5">
                <MudPaper Class="pa-2">
                    <MudGrid Spacing="2">
                        <MudItem xs="12"><MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Patient Information"]</MudText></MudItem>
                        <MudItem xs="12">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@(_PatientService?.PatientData?.Name ?? @Localizer["N/A"])</MudText>
                            </MudItem>
                        <MudItem xs="12">
                            <MudText Typo="Typo.body2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Gender"]:<b>@(_PatientService?.PatientData?.Sex ?? @Localizer["N/A"])</b></MudText>
                            <MudText Typo="Typo.body2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["DOB"]: <b>@(_PatientService?.PatientData?.DOB?.ToShortDateString() ?? @Localizer["N/A"])</b></MudText>
                            <MudText Typo="Typo.body2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Primary Care Physician"]: <b>@(_PatientService?.PatientData?.PCPName ?? @Localizer["N/A"])</b></MudText>
                        </MudItem>
                        <MudItem xs="12"><MudDivider Light></MudDivider></MudItem>
                        <MudItem>
                            <MudText Typo="Typo.body2" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Visit Type"]: <b>@(_PatientService?.VisitType ?? @Localizer["N/A"])</b></MudText>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>
            <MudItem xs="7"> </MudItem>
            <MudItem xs="12">
                <MudPaper Class="pa-2">
                    <MudGrid Spacing="2">
                        <MudItem xs="12"><MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Patient Vitals"]</MudText></MudItem>
                        <MudItem xs="4">

                            <SfChart width="240px" Height="200px" SubTitle="Temperature">
                                <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                   LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                </ChartPrimaryXAxis>
                                <ChartPrimaryYAxis Title="@Localizer["Temperature"]">
                                    <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                </ChartPrimaryYAxis>
                                <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Value: ${point.y}" />
                                <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                <ChartSeriesCollection>
                                    <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Temperature" Type="ChartSeriesType.Line" Name="Temperature">
                                        <ChartMarker Visible="true" Height="10" Width="10" />
                                    </ChartSeries>
                                </ChartSeriesCollection>
                            </SfChart>
                        </MudItem>
                        <MudItem xs="4">
                            <SfChart Width="240px" Height="200px">
                                <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                   LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                </ChartPrimaryXAxis>
                                <ChartPrimaryYAxis Title="Height" ><ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle></ChartPrimaryYAxis>
                                <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Value: ${point.y}" />
                                <ChartSeriesCollection>
                                    <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Height"
                                                 Type="ChartSeriesType.Line" Name="Height">

                                        <ChartMarker Visible="true" Height="10" Width="10" />

                                    </ChartSeries>
                                </ChartSeriesCollection>
                            </SfChart>

                        </MudItem>
                        <MudItem xs="4">
                            <SfChart width="240px" Height="200px">
                                <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                   LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                </ChartPrimaryXAxis>
                                <ChartPrimaryYAxis Title="@Localizer["Pulse"]">
                                    <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                </ChartPrimaryYAxis>
                                <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Value: ${point.y}" />
                                <ChartSeriesCollection>
                                    <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Pulse" Type="ChartSeriesType.Line" Name="Pulse">
                                        <ChartMarker Visible="true" Height="10" Width="10" />
                                    </ChartSeries>
                                </ChartSeriesCollection>
                            </SfChart>
                        </MudItem>
                        <MudItem xs="4">
                            <SfChart width="240px" Height="200px">
                                <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                   LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                </ChartPrimaryXAxis>
                                <ChartPrimaryYAxis Title="@Localizer["Weight"]">
                                    <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                </ChartPrimaryYAxis>
                                <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Value: ${point.y}" />
                                <ChartSeriesCollection>
                                    <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Weight" Type="ChartSeriesType.Line" Name="Weight">
                                        <ChartMarker Visible="true" Height="10" Width="10" />
                                    </ChartSeries>
                                </ChartSeriesCollection>
                            </SfChart>
                        </MudItem>
                        <MudItem xs="4">
                            <SfChart Width="240px" Height="200px">
                                <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                   LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                </ChartPrimaryXAxis>
                                <ChartPrimaryYAxis Title="@Localizer["Blood Pressure"]">
                                    <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                </ChartPrimaryYAxis>
                                <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Value: ${point.y}" />
                                <ChartSeriesCollection>
                                    <ChartSeries DataSource="@formattedVitals" XName="CreatedDate" YName="Systolic" Type="ChartSeriesType.Line" Name="Systolic">
                                        <ChartMarker Visible="true" Height="10" Width="10" />
                                    </ChartSeries>
                                    <ChartSeries DataSource="@formattedVitals" XName="CreatedDate" YName="Diastolic" Type="ChartSeriesType.Line" Name="Diastolic">
                                        <ChartMarker Visible="true" Height="10" Width="10" />
                                    </ChartSeries>
                                </ChartSeriesCollection>
                            </SfChart>
                        </MudItem>
                        <MudItem xs="4">
                            <MudPaper Class="pa-2" Width="200px" Height="200px" Style="margin: auto; display: flex; justify-content: center; align-items: center;">
                                <MudGrid Spacing="2">
                                    <MudItem xs="12" Style="height: 0px; display: flex; justify-content: center; align-items: center; margin: auto;">
                                        <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">
                                            @Localizer["Most Recent"]
                                        </MudText>
                                    </MudItem>

                                    <MudItem xs="12"><MudDivider></MudDivider></MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Temperature"]</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Class="d-flex justify-center align-center">@(MostRecentVitals?.Temperature ?? @Localizer["N/A"])</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1">@Localizer["Height (Feet)"]</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Class="d-flex justify-center align-center">@(MostRecentVitals?.Height ?? @Localizer["N/A"])</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Pulse Rate"]</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Class="d-flex justify-center align-center">@(MostRecentVitals?.Pulse ?? @Localizer["N/A"])</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Weight (KG)"]</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Class="d-flex justify-center align-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@(MostRecentVitals?.Weight ?? @Localizer["N/A"])</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["BP"]</MudText>
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudText Typo="Typo.body1" Class="d-flex justify-center align-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@(MostRecentVitals?.BP ?? @Localizer["N/A"])</MudText>
                                    </MudItem>
                                </MudGrid>
                            </MudPaper>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>
            <MudItem xs="6">
                <MudPaper Class="pa-1">
                    <MudItem xs="12" Class="pa-1">
                        <MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Medical History"]</MudText>
                    </MudItem>
                    <MudGrid Spacing="2" Class="pa-1">
                        <MudItem xs="8">
                            <MudText Typo="Typo.h6" Class="font-weight-bold" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["History"]</MudText>
                        </MudItem>
                        <MudItem xs="4">
                            <MudText Typo="Typo.h6" Class="font-weight-bold" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Created Date"]</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudDivider Light></MudDivider>
                        </MudItem>
                        <MudItem xs="12">
                        <MudList T="MedicalHistoryDTO">
                        @if (medicalHistory != null && medicalHistory.Any())
                        {
                            @foreach (var member in medicalHistory)
                            {
                                <MudListItem Class="pa-1">
                                            <MudGrid>
                                <MudItem xs="8">
                                                    <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.History</MudText>
                                </MudItem>
                                <MudItem xs="4">
                                     @(member.CreatedDate.HasValue ? member.CreatedDate.Value.ToString("MMM dd, yyyy") : "N/A")
                                </MudItem>
                                </MudGrid>
                                </MudListItem>
                            }
                        }
                        else
                        {
                        <MudListItem>
                                        <MudText Typo="Typo.body1" Class="text-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">No medical history found</MudText>
                        </MudListItem>
                         }
                            </MudList>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

            </MudItem>
            <MudItem xs="6">
                <MudPaper Class="pa-1">
                    <MudItem xs="12" Class="pa-1">
                        <MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Medications"]</MudText>
                    </MudItem>
                    <MudGrid Spacing="2" Class="pa-1">
                        <MudItem xs="12">
                            <MudText Typo="Typo.h6" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Class="font-weight-bold">@Localizer["Drug Details"]</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudDivider Light></MudDivider>
                        </MudItem>
                        <MudItem xs="12">
                        <MudList T="ActiveMedication">
                                @if (activeMedications != null && activeMedications.Any())
                                {
                                    @foreach (var member in activeMedications)
                                    {
                                        <MudListItem>
                                            <MudGrid Spacing="2">
                                                <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.DrugDetails</MudText>
                                            </MudGrid>
                                       </MudListItem>
                                    }
                                }
                                else
                                {
                                <MudListItem>
                                        <MudText Typo="Typo.body1" Class="text-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">No medications history found</MudText>
                                </MudListItem>
                                 }
                        </MudList>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>
            <MudItem xs="6">
                <MudPaper Class="pa-1">
                    <MudItem xs="12" Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                        <MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Allergies"]</MudText>
                    </MudItem>
                    <MudGrid Spacing="2" Class="pa-1">
                        <MudItem xs="8">
                            <MudText Typo="Typo.h6" Class="font-weight-bold" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Drug Name"]</MudText>
                        </MudItem>
                        <MudItem xs="4">
                            <MudText Typo="Typo.h6" Class="font-weight-bold d-flex justify-center align-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@Localizer["Classification"]</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudDivider Light></MudDivider>
                        </MudItem>
                        <MudItem xs="12">
                        <MudList T="Allergies">
                                @if (allergies != null && allergies.Any())
                                {
                                @foreach (var member in allergies)
                                {
                                       <MudListItem Class="pa-1">
                                        <MudGrid>
                                        <MudItem xs="8">
                                                    <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.DrugName</MudText>
                                        </MudItem>
                                        <MudItem xs="4">
                                                    <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.Classification</MudText>
                                        </MudItem>
                                        </MudGrid>
                                    </MudListItem>
                                }
                             }
                                else
                                {
                                    <MudListItem>
                                        <MudText Typo="Typo.body1" Class="text-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">No allergies history found</MudText>
                                    </MudListItem>
                                }
                        </MudList>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </MudItem>
            <MudItem xs="6">
                <MudPaper Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">
                    <MudItem xs="12" Class="pa-1">
                        <MudText Typo="Typo.h6" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif" Color="Color.Primary">@Localizer["Family History"]</MudText>
                    </MudItem>
                    <MudGrid Spacing="2" Class="pa-1">
                        <MudItem xs="6" >
                            <MudText Typo="Typo.h6" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif" Class="font-weight-bold">@Localizer["Relation"]</MudText>
                        </MudItem>
                        <MudItem xs="6">
                            <MudText Typo="Typo.h6" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif" Class="font-weight-bold">@Localizer["Status"]</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudDivider Light></MudDivider>
                        </MudItem>
                        <MudItem xs="12">
                       <MudList T="FamilyMember">
                                @if (familyMembers != null && familyMembers.Any())
                                {
                                     @foreach (var member in familyMembers)
                                     {
                                        <MudListItem Class="pa-1">
                                            <MudGrid>
                                                <MudItem xs="6">
                                                    <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.Relation</MudText>
                                                </MudItem>
                                                <MudItem xs="6">
                                                    <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">@member.Status</MudText>
                                                </MudItem>
                                                </MudGrid>
                                        </MudListItem>
                                     }
                                }
                                else
                                {
                                    <MudListItem>
                                        <MudText Typo="Typo.body1" Class="text-center" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif">No family history found</MudText>
                                    </MudListItem>
                                }
                        </MudList>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

            </MudItem>
        </MudGrid>
    </MudContainer>
</div>

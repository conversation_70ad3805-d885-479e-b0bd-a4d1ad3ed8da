﻿using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
using System.Numerics;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using static MudBlazor.CategoryTypes;
using static System.Net.WebRequestMethods;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIViewModels.ViewModels;
using TeyaWebApp.Services;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Components.Web;
using System.Net.Mail;

namespace TeyaWebApp.Components.Pages
{
    public partial class Config
    {
        private bool isLoading = true;
        private string? errorMessage = null;
        private string? username;
        private Guid selectedOrganizationId;
        private MudTextField<string> roleNameInput;
        private MudTextField<string> facilityNameInput;
        private List<string> Countries = new();
        private List<Organization> Organizations = new();
        private List<Organization> ActiveOrganization = new();
        private Organization activeOrganization = new();
        private readonly string adminRole = "Admin";
        private List<Role> ActiveRoles = new();
        private List<Role> NonAdminRoles = new();
        string NewFacilityName = string.Empty;
        string NewRoleName = string.Empty;
        private List<Facility> ActiveUserFacilities = new();
        private List<VisitType> VisitTypes = new();
        private SfGrid<Organization>? organizationGrid;
        private SfGrid<Facility>? facilityGrid;
        private SfGrid<Role>? roleGrid;
        private SfGrid<VisitType>? visitTypeGrid;
        private bool Subscription = false;
        private MudForm form;
        private Organization? selectedOrganization;

        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PageRoleMappingData> PageUrls = new();
        private SfGrid<PageRoleMappingData>? pageRoleMappingGrid;
        private Role? _selectedRole;
        private Role? selectedRole
        {
            get => _selectedRole;
            set
            {
                if (_selectedRole != value)
                {
                    _selectedRole = value;
                    _ = OnRoleChanged(value);
                }
            }
        }

        private bool selectAllChecked = false;
        private Organization Organization { get; set; } = new Organization();
        private Role Role { get; set; } = new Role();
        private Facility Facility { get; set; } = new Facility();
        [Inject] private ICountryService CountryService { get; set; }
        [Inject] private ILogger<Config> Logger { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private IFacilityService FacilityService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IVisitTypeService VisitTypeService { get; set; }
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPagePathService PagePathService { get; set; }
        [Inject] private RoleMappingState RoleMappingState { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }

        private Organization OriginalOrganization = new Organization();
        private Facility OriginalFacility = new Facility();
        private bool ShouldShowPageRoleMappingGrid => selectedRole != null;
        private bool HasPhoneNumberError = false;
        private string PhoneNumberErrorText = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            await LoadMembersAsync();
            await GetActiveOrganization();
            await GetActiveUserFacilitiesList();
            await GetActiveRolesList();
            await LoadVisitTypes();
            NonAdminRoles = ActiveRoles.Where(r => r.RoleName != adminRole).ToList();
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(Organization.OrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == "Enterprise";

            Role.OrganizationID = ActiveOrganization.FirstOrDefault().OrganizationId;
            Facility.OrganizationId = ActiveOrganization.FirstOrDefault().OrganizationId;
            var countries = await CountryService.GetAllCountriesAsync();
            Countries = countries.Select(c => c.CountryName).ToList();

            Countries.Sort();
            StateHasChanged();
        }

        /// <summary>
        /// Loads the visit types for a given organization.
        /// </summary>
        /// <param name="selectedOrganizationId">The ID of the selected organization.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        private async Task LoadVisitTypes()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                string s = User.OrganizationName;
                selectedOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                VisitTypes = await VisitTypeService.GetVisitTypesByOrganizationIdAsync(selectedOrganizationId, Subscription);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingVisitTypes"], ex.Message);
                Snackbar.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task OnActionBegin(ActionEventArgs<VisitType> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Delete"],
                    $"Are you sure you want to delete the visit type '{args.Data.VisitName}'? This action cannot be undone.",
                    yesText: Localizer["OK"],
                    cancelText: Localizer["Cancel"]
                );

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save &&
                args.Action.Equals("add", StringComparison.OrdinalIgnoreCase))
            {
                if (string.IsNullOrWhiteSpace(args.Data.VisitName) || string.IsNullOrWhiteSpace(args.Data.CPTCode))
                {
                    args.Cancel = true;
                    Snackbar.Add(Localizer["Please provide both Visit Type name and CPT code."], Severity.Warning);
                    return;
                }

                args.Data.VisitName = args.Data.VisitName.Trim();

                if (args.Data.VisitName.Any(char.IsDigit))
                {
                    args.Cancel = true;
                    Snackbar.Add(Localizer["Visit name cannot contain numerical characters."], Severity.Warning);
                    return;
                }

                if (!Regex.IsMatch(args.Data.VisitName, @"^[a-zA-Z\s\-_]+$"))
                {
                    args.Cancel = true;
                    Snackbar.Add(Localizer["Visit name can only contain letters, spaces, hyphens, and underscores."], Severity.Warning);
                    return;
                }

                if (!string.IsNullOrWhiteSpace(args.Data.VisitName))
                {
                    bool isDuplicateVisitType = VisitTypes.Any(v => v.VisitName.Equals(args.Data.VisitName.Trim(), StringComparison.OrdinalIgnoreCase));

                    if (isDuplicateVisitType)
                    {
                        args.Cancel = true;

                        await DialogService.ShowMessageBox(
                            Localizer["Duplicate Visit Type Name"],
                            Localizer["Please provide a unique Visit Type name."],
                            yesText: Localizer["OK"]
                        );

                        return;
                    }
                }

                bool? addResult = await DialogService.ShowMessageBox(
                    Localizer["Confirm Add"], // Title
                    $"Do you want to add the visit type '{args.Data.VisitName}' with CPT code '{args.Data.CPTCode}'?",
                    yesText: Localizer["OK"],
                    cancelText: Localizer["Cancel"]
                );

                if (addResult != true)
                {
                    args.Cancel = true; 
                    return;
                }
            }
        }

        private async Task OnGridAction(ActionEventArgs<VisitType> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    if (await VisitTypeService.DeleteVisitTypeAsync(selectedOrganizationId, args.Data.VisitName, Subscription))
                    {
                        VisitTypes.Remove(args.Data);
                        await visitTypeGrid.Refresh();
                        Snackbar.Add(Localizer["Visit type deleted successfully."], Severity.Success);
                        StateHasChanged();
                    }
                    else
                    {
                        Snackbar.Add(Localizer["Failed to delete visit type."], Severity.Error);
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (args.Action.Equals("edit", StringComparison.OrdinalIgnoreCase))
                    {
                        if (!string.IsNullOrWhiteSpace(args.Data.CPTCode) &&
                            await VisitTypeService.UpdateCptCodeAsync(selectedOrganizationId, args.Data.VisitName, args.Data.CPTCode, Subscription))
                        {
                            VisitTypes.FirstOrDefault(v => v.VisitName == args.Data.VisitName)!.CPTCode = args.Data.CPTCode;
                            await visitTypeGrid.Refresh();
                            Snackbar.Add(Localizer["Visit type updated successfully."], Severity.Success);
                            StateHasChanged();
                        }
                        else
                        {
                            Snackbar.Add(Localizer["Failed to update visit type."], Severity.Error);
                            await LoadVisitTypes();
                        }
                    }
                    else if (args.Action.Equals("add", StringComparison.OrdinalIgnoreCase))
                    {
                        var newVisit = new VisitType
                        {
                            ID = Guid.NewGuid(),
                            OrganizationId = selectedOrganizationId,
                            VisitName = args.Data.VisitName,
                            CPTCode = args.Data.CPTCode
                        };

                        if (await VisitTypeService.AddVisitTypeAsync(newVisit))
                        {
                            await LoadVisitTypes();
                            Snackbar.Add(Localizer["Visit type added successfully."], Severity.Success);
                            StateHasChanged();
                        }
                        else
                        {
                            Snackbar.Add(Localizer["Failed to add visit type."], Severity.Error);
                            await LoadVisitTypes();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in OnGridAction");
                Snackbar.Add(Localizer["An error occurred while processing the request."], Severity.Error);
            }
        }

        private async Task LoadMembersAsync()
        {
            try
            {
                var pagePaths = await PagePathService.GetPagePathsAsync();

                PageUrls = pagePaths.Select(p => new PageRoleMappingData
                {
                    PagePath = p.PagePathValue
                }).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading organizations: {ex.Message}");
                Snackbar.Add(Localizer["Error loading page paths."], Severity.Error);
            }
        }

        private async Task GetActiveOrganization()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                username = User.id;
                var activeUserOrganization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                ActiveOrganization = activeUserOrganization.ToList();

                var activeOrg = ActiveOrganization.FirstOrDefault();
                if (activeOrg != null)
                {
                    Organization.OrganizationId = activeOrg.OrganizationId;
                    Organization.OrganizationName = activeOrg.OrganizationName;
                    Organization.Country = activeOrg.Country;
                    Organization.Address = activeOrg.Address;
                    Organization.ContactNumber = activeOrg.ContactNumber;
                    Organization.Email = activeOrg.Email;

                    // Store original values for change detection
                    OriginalOrganization = new Organization
                    {
                        OrganizationId = activeOrg.OrganizationId,
                        OrganizationName = activeOrg.OrganizationName,
                        Country = activeOrg.Country,
                        Address = activeOrg.Address,
                        ContactNumber = activeOrg.ContactNumber,
                        Email = activeOrg.Email
                    };
                }
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingOrganizations"], ex.Message);
                Snackbar.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task GetActiveRolesList()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var roles = await RoleService.GetAllRolesByOrgIdAsync(activeUserOrganizationId, Subscription);
                ActiveRoles = roles.Where(role => role.IsActive).ToList();
                NonAdminRoles = ActiveRoles.Where(r => r.RoleName != adminRole).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingRoles"], ex.Message);
                Snackbar.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isLoading = false;
            }
        }

        private bool HasOrganizationChanges()
        {
            return OriginalOrganization.OrganizationName != Organization.OrganizationName ||
                   OriginalOrganization.Country != Organization.Country ||
                   OriginalOrganization.Address != Organization.Address ||
                   OriginalOrganization.ContactNumber != Organization.ContactNumber ||
                   OriginalOrganization.Email != Organization.Email;
        }

        // Add helper method to detect facility changes
        private bool HasFacilityChanges()
        {
            return OriginalFacility.FacilityName != Facility.FacilityName ||
                   OriginalFacility.StreetName != Facility.StreetName ||
                   OriginalFacility.City != Facility.City ||
                   OriginalFacility.Zipcode != Facility.Zipcode ||
                   OriginalFacility.State != Facility.State ||
                   OriginalFacility.Country != Facility.Country;
        }
        private async Task GetActiveUserFacilitiesList()
        {
            try
            {
                isLoading = true;
                errorMessage = null;
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var facilities = await FacilityService.GetAllFacilitiesByOrgIdAsync(activeUserOrganizationId, Subscription);
                ActiveUserFacilities = facilities.ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingFacilities"], ex.Message);
                Snackbar.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isLoading = false;
            }
        }

        private async Task RefreshOrganizationsGridAsync()
        {
            try
            {
                await GetActiveOrganization();
                StateHasChanged();
                Organization.OrganizationId = Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing organizations grid: {Message}", ex.Message);
                Snackbar.Add(Localizer["Error refreshing organizations."], Severity.Error);
            }
        }

        /// <summary>
        /// Validates US phone number format - must be exactly in XXX-XXX-XXXX format
        /// </summary>
        /// <param name="phoneNumber">Phone number to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        private void ValidateContactNumber(FocusEventArgs args)
        {
            var phone = Organization?.ContactNumber ?? string.Empty;

            if (!IsValidPhoneNumber(phone))
            {
                HasPhoneNumberError = true;
                PhoneNumberErrorText = "Phone number must be in the format ************ (10 digits)";
            }
            else
            {
                HasPhoneNumberError = false;
                PhoneNumberErrorText = string.Empty;
            }
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            if (phoneNumber.Length != 12)
                return false;

            if (phoneNumber[3] != '-' || phoneNumber[7] != '-')
                return false;

            string digits = phoneNumber.Replace("-", "");

            return digits.Length == 10 && digits.All(char.IsDigit);
        }

        /// <summary>
        /// Validates email format using MailAddress class
        /// </summary>
        /// <param name="email">Email address to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        private bool IsValidEmailAddress(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var mailAddress = new System.Net.Mail.MailAddress(email.Trim());
                return mailAddress.Address == email.Trim();
            }
            catch
            {
                return false;
            }
        }

        private async Task SaveOrganizationForm()
        {
            try
            {
                // 1. Validate required fields
                if (string.IsNullOrWhiteSpace(Organization.OrganizationName))
                {
                    Snackbar.Add(Localizer["Organization name is required."], Severity.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(Organization.Country))
                {
                    Snackbar.Add(Localizer["Country is required."], Severity.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(Organization.Address))
                {
                    Snackbar.Add(Localizer["Address is required."], Severity.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(Organization.ContactNumber))
                {
                    Snackbar.Add(Localizer["Contact Number is required."], Severity.Warning);
                    return;
                }
                if (!IsValidPhoneNumber(Organization.ContactNumber))
                {
                    Snackbar.Add("Please enter a valid Phone number", Severity.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(Organization.Email))
                {
                    Snackbar.Add(Localizer["Email is required."], Severity.Warning);
                    return;
                }
                if (!IsValidEmailAddress(Organization.Email))
                {
                    Snackbar.Add(Localizer["Please enter a valid email address."], Severity.Warning);
                    return;
                }

                // 2. Check if any changes exist
                bool hasChanges = HasOrganizationChanges();

                if (!hasChanges)
                {
                    // No changes - nothing to save
                    await DialogService.ShowMessageBox(
                        title: Localizer["No Changes"],
                        message: Localizer["No Changes Detected"]);
                    return;
                }

                // 3. Show confirmation dialog if changes exist
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Save"],
                    Localizer["Save Changes Confirmation"],
                    yesText: Localizer["Save"],
                    noText: Localizer["Cancel"]
                );

                if (result != true)
                {
                    return;
                }

                // 4. Proceed with saving
                if (Organization.OrganizationId != Guid.Empty)
                {
                    await OrganizationService.UpdateOrganizationByIdAsync(Organization.OrganizationId, Organization);
                    Logger.LogInformation($"Updated Organization: {Organization.OrganizationName}");
                    Snackbar.Add(Localizer["Organization updated successfully."], Severity.Success);
                }

                await RefreshOrganizationsGridAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingOrganizationData"]);
                Snackbar.Add(Localizer["Failed to save organization."], Severity.Error);
            }
        }

        // Update CancelOrganizationForm method with changes detection
        private async Task CancelOrganizationForm()
        {
            try
            {
                // Check if there are any unsaved changes
                bool hasChanges = HasOrganizationChanges();

                if (hasChanges)
                {
                    // Show confirmation dialog if changes exist
                    var confirmed = await DialogService.ShowMessageBox(
                        title: Localizer["Confirm Cancel"],
                        message: Localizer["You have unsaved changes. Are you sure you want to cancel?"],
                        yesText: Localizer["Discard Changes"],
                        noText: Localizer["Continue Editing"]);

                    if (confirmed != true)
                    {
                        return; // User chose to continue editing
                    }
                }

                // Reset to original values
                if (ActiveOrganization != null && ActiveOrganization.Any())
                {
                    var activeOrg = ActiveOrganization.FirstOrDefault();
                    Organization.OrganizationId = activeOrg.OrganizationId;
                    Organization.OrganizationName = activeOrg.OrganizationName;
                    Organization.Country = activeOrg.Country;
                    Organization.Address = activeOrg.Address;
                    Organization.ContactNumber = activeOrg.ContactNumber;
                    Organization.Email = activeOrg.Email;
                }
                else
                {
                    await GetActiveOrganization();
                }

                HasPhoneNumberError = false;
                PhoneNumberErrorText = string.Empty;

                Logger.LogInformation("Organization form reset to original data.");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while canceling organization form.");
                Snackbar.Add(Localizer["Error occurred while cancelling."], Severity.Error);
                await GetActiveOrganization();
            }
        }

        private async Task ShowDeleteConfirmationForFacility()
        {
            try
            {
                if (facilityGrid != null)
                {
                    var selectedFacilities = await facilityGrid.GetSelectedRecordsAsync();

                    if (selectedFacilities == null || !selectedFacilities.Any())
                    {
                        Logger.LogWarning("No facilities selected for deletion.");
                        Snackbar.Add(Localizer["No facilities selected for deletion."], Severity.Warning);
                        return;
                    }

                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["Confirm Delete"],
                        Localizer["Are you sure you want to delete the selected facility?"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["Cancel"]
                    );

                    if (result == true)
                    {
                        await DeleteSelectedFacilities();
                    }
                }
                else
                {
                    Logger.LogError("Facility grid is not initialized.");
                    Snackbar.Add(Localizer["Facility grid is not initialized."], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while checking selected facilities.");
                Snackbar.Add(Localizer["Error occurred while processing facilities."], Severity.Error);
            }
        }

        private async Task ShowDeleteConfirmationForRole()
        {
            try
            {
                if (roleGrid != null)
                {
                    var selectedRoles = await roleGrid.GetSelectedRecordsAsync();

                    if (selectedRoles == null || !selectedRoles.Any())
                    {
                        Logger.LogWarning("No roles selected for deletion.");
                        Snackbar.Add(Localizer["No roles selected for deletion."], Severity.Warning);
                        return;
                    }

                    bool? result = await DialogService.ShowMessageBox(
                        Localizer["Confirm Delete"],
                        Localizer["Are you sure you want to delete the selected role?"],
                        yesText: Localizer["Yes"],
                        noText: Localizer["Cancel"]
                    );

                    if (result == true)
                    {
                        await DeleteSelectedRoles();
                    }
                }
                else
                {
                    Logger.LogError("Role grid is not initialized.");
                    Snackbar.Add(Localizer["Role grid is not initialized."], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while checking selected roles.");
                Snackbar.Add(Localizer["Error occurred while processing roles."], Severity.Error);
            }
        }

        private async Task DeleteSelectedFacilities()
        {
            try
            {
                if (facilityGrid != null)
                {
                    var selectedFacilities = await facilityGrid.GetSelectedRecordsAsync();

                    if (selectedFacilities != null && selectedFacilities.Any())
                    {
                        foreach (var facility in selectedFacilities)
                        {
                            facility.UpdatedDate = DateTime.Now;
                            facility.UpdatedBy = Guid.Parse(username);
                            facility.IsActive = false;
                            await FacilityService.UpdateFacilityByIdAsync(facility.FacilityId, facility);
                            Logger.LogInformation($"Inactive Facility: {facility.FacilityName}");
                        }
                        await RefreshFacilitiesGridAsync();
                        Snackbar.Add(Localizer["Selected facilities deleted successfully."], Severity.Success);
                    }
                    else
                    {
                        Logger.LogWarning("No facilities selected for deletion.");
                        Snackbar.Add(Localizer["No facilities selected for deletion."], Severity.Warning);
                    }
                }
                else
                {
                    Logger.LogError("Facility grid is not initialized.");
                    Snackbar.Add(Localizer["Facility grid is not initialized."], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while deleting selected facilities.");
                Snackbar.Add(Localizer["Error occurred while deleting facilities."], Severity.Error);
            }
        }

        private async Task DeleteSelectedRoles()
        {
            try
            {
                if (roleGrid != null)
                {
                    var selectedRoles = await roleGrid.GetSelectedRecordsAsync();
                    if (selectedRoles != null && selectedRoles.Any())
                    {
                        foreach (var role in selectedRoles)
                        {
                            role.IsActive = false;
                            role.UpdatedDate = DateTime.Now;
                            role.UpdatedBy = Guid.Parse(username);
                            await RoleService.UpdateRoleByIdAsync(role.RoleId, role);
                            Logger.LogInformation($"Inactive Role: {role.RoleName}");
                        }
                        await RefreshRolesGridAsync();
                        Snackbar.Add(Localizer["Selected roles deleted successfully."], Severity.Success);
                        StateHasChanged();
                    }
                    else
                    {
                        Logger.LogWarning("No roles selected for deletion.");
                        Snackbar.Add(Localizer["No roles selected for deletion."], Severity.Warning);
                    }
                }
                else
                {
                    Logger.LogError("Role grid is not initialized.");
                    Snackbar.Add(Localizer["Role grid is not initialized."], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while deleting selected roles.");
                Snackbar.Add(Localizer["Error occurred while deleting roles."], Severity.Error);
            }
        }

        private async Task RefreshRolesGridAsync()
        {
            try
            {
                await GetActiveRolesList();

                // Reset the entire Role object and set the organization ID
                Role = new Role();
                Role.OrganizationID = ActiveOrganization?.FirstOrDefault()?.OrganizationId ?? Guid.Empty;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing roles grid: {Message}", ex.Message);
                Snackbar.Add(Localizer["Error refreshing roles."], Severity.Error);
            }
        }

        private async Task SaveRoleAsync()
        {
            if (string.IsNullOrWhiteSpace(Role.RoleName))
            {
                Snackbar.Add(Localizer["Role name is required."], Severity.Warning);
                return;
            }

            Role.RoleName = Role.RoleName.Trim();

            if (Role.RoleName.Any(char.IsDigit))
            {
                Snackbar.Add(Localizer["Role name cannot contain numerical characters."], Severity.Warning);
                return;
            }

            if (!Regex.IsMatch(Role.RoleName, @"^[a-zA-Z\s\-_]+$"))
            {
                Snackbar.Add(Localizer["Role name can only contain letters, spaces, hyphens, and underscores."], Severity.Warning);
                return;
            }

            NewRoleName = Role.RoleName;
            bool isDuplicate = ActiveRoles.Any(r => r.RoleName.Equals(NewRoleName.Trim(), StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                await DialogService.ShowMessageBox(
                    Localizer["Duplicate Role Name"],
                    Localizer["Please provide a unique role name."],
                    yesText: Localizer["OK"]
                );
                return;
            }
            await SaveRoleForm();
        }

        private async Task SaveRoleForm()
        {
            try
            {
                if (Role.RoleId == Guid.Empty)
                {
                    Role.RoleId = Guid.NewGuid();
                }
                var registeredRole = await RoleService.RegisterRoleAsync(Role);
                Logger.LogInformation(Localizer["RoleRegistered"], registeredRole.RoleName);
                Snackbar.Add(Localizer["Role saved successfully."], Severity.Success);
                await RefreshRolesGridAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingRoleData"]);
                Snackbar.Add(Localizer["Failed to save role."], Severity.Error);
            }
        }

        private async Task RefreshFacilitiesGridAsync()
        {
            try
            {
                await GetActiveUserFacilitiesList();
                StateHasChanged();
                Facility.FacilityId = Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError("Error refreshing facilities grid: {Message}", ex.Message);
                Snackbar.Add(Localizer["Error refreshing facilities."], Severity.Error);
            }
        }

        private async Task SaveFacilityAsync()
        {
            // 1. Validate required fields
            if (string.IsNullOrWhiteSpace(Facility.FacilityName))
            {
                Snackbar.Add(Localizer["Facility name is required."], Severity.Warning);
                return;
            }
            if (string.IsNullOrWhiteSpace(Facility.StreetName))
            {
                Snackbar.Add(Localizer["Street name is required."], Severity.Warning);
                return;
            }
            if (string.IsNullOrWhiteSpace(Facility.City))
            {
                Snackbar.Add(Localizer["City is required."], Severity.Warning);
                return;
            }
            if (string.IsNullOrWhiteSpace(Facility.Zipcode))
            {
                Snackbar.Add(Localizer["Zip code is required."], Severity.Warning);
                return;
            }

            // Validate zipcode format - only accept numerical values
            if (!System.Text.RegularExpressions.Regex.IsMatch(Facility.Zipcode.Trim(), @"^\d+$"))
            {
                Snackbar.Add(Localizer["Zip code must contain only numericals."], Severity.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(Facility.State))
            {
                Snackbar.Add(Localizer["State is required."], Severity.Warning);
                return;
            }
            if (string.IsNullOrWhiteSpace(Facility.Country))
            {
                Snackbar.Add(Localizer["Country is required."], Severity.Warning);
                return;
            }

            // 2. For new facilities, check duplicates
            if (Facility.FacilityId == Guid.Empty)
            {
                NewFacilityName = Facility.FacilityName.Trim();
                bool isDuplicate = ActiveUserFacilities.Any(f => f.FacilityName.Equals(NewFacilityName.Trim(), StringComparison.OrdinalIgnoreCase));

                if (isDuplicate)
                {
                    await DialogService.ShowMessageBox(
                        Localizer["Duplicate Facility Name"],
                        Localizer["Please provide a unique facility name."],
                        yesText: Localizer["OK"]
                    );
                    return;
                }
            }

            // 3. For existing facilities, check if there are changes
            if (Facility.FacilityId != Guid.Empty)
            {
                bool hasChanges = HasFacilityChanges();

                if (!hasChanges)
                {
                    // No changes - nothing to save
                    await DialogService.ShowMessageBox(
                        title: Localizer["No Changes"],
                        message: Localizer["No Changes Detected"]);
                    return;
                }
            }

            // 4. Show confirmation dialog
            string confirmationMessage = Facility.FacilityId == Guid.Empty
                ? Localizer["Do you want to add the facility?"]
                : Localizer["Save Changes Confirmation"];

            bool? result = await DialogService.ShowMessageBox(
                Localizer["Confirm Add"],
                confirmationMessage,
                yesText: Localizer["Add"],
                cancelText: Localizer["Cancel"]
            );

            if (result != true)
            {
                return;
            }

            await SaveFacilityForm();
        }

        private async Task SaveFacilityForm()
        {
            try
            {
                Facility.FacilityId = Guid.NewGuid();
                var registeredFacility = await FacilityService.RegisterFacilityAsync(Facility);
                Logger.LogInformation(Localizer["FacilityRegistered"], registeredFacility.FacilityName);
                Snackbar.Add(Localizer["Facility added successfully."], Severity.Success);
                await RefreshFacilitiesGridAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingFacilityData"]);
                Snackbar.Add(Localizer["Failed to save facility."], Severity.Error);
                Logger.LogError("Exception Details: {Message}", ex.Message);
                Logger.LogError("Stack Trace: {StackTrace}", ex.StackTrace);
            }
        }

        private async Task CancelFacilityForm()
        {
            try
            {
                // Check if there are any unsaved changes
                bool hasChanges = HasFacilityChanges();

                if (hasChanges)
                {
                    // Show confirmation dialog if changes exist
                    var confirmed = await DialogService.ShowMessageBox(
                        title: Localizer["Confirm Cancel"],
                        message: Localizer["You have unsaved changes. Are you sure you want to cancel?"],
                        yesText: Localizer["Discard Changes"],
                        noText: Localizer["Continue Editing"]);

                    if (confirmed != true)
                    {
                        return; // User chose to continue editing
                    }
                }

                // Reset the form values
                Facility = new Facility();
                Facility.OrganizationId = ActiveOrganization?.FirstOrDefault()?.OrganizationId ?? Guid.Empty;

                // Reset original facility values
                OriginalFacility = new Facility();

                Logger.LogInformation("Facility form reset to original data.");
                Snackbar.Add(Localizer["Changes cancelled."], Severity.Info);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while canceling facility form.");
                Snackbar.Add(Localizer["Error occurred while cancelling."], Severity.Error);
            }
        }

        // Add method to set original facility values when editing
        private void SetOriginalFacilityValues(Facility facility)
        {
            OriginalFacility = new Facility
            {
                FacilityId = facility.FacilityId,
                FacilityName = facility.FacilityName,
                StreetName = facility.StreetName,
                City = facility.City,
                Zipcode = facility.Zipcode,
                State = facility.State,
                Country = facility.Country,
                OrganizationId = facility.OrganizationId
            };
        }

        /// <summary>
        /// Handles the role change event and updates the page role mappings.
        /// </summary>
        /// <param name="role">The selected role.</param>
        private async Task OnRoleChanged(Role role)
        {
            try
            {
                selectedRole = role;

                // Clear the grid data when no role is selected
                if (selectedRole == null)
                {
                    PageRoleMappings.Clear();
                    foreach (var page in PageUrls)
                    {
                        page.HasAccess = false;
                        page.IsModified = false;
                    }

                    if (pageRoleMappingGrid != null)
                    {
                        await pageRoleMappingGrid.Refresh();
                    }
                    UpdateSelectAllState();
                    StateHasChanged();
                    return;
                }

                activeOrganization = (await OrganizationService.GetOrganizationByIdAsync(selectedRole.OrganizationID));
                selectedOrganization = activeOrganization;

                // Get role-specific mappings
                var roleMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(selectedRole.RoleId, activeOrganization.OrganizationId, Subscription);
                PageRoleMappings = roleMappings.ToList();

                // Reset all pages to false first
                foreach (var page in PageUrls)
                {
                    page.HasAccess = false;
                    page.IsModified = false;
                }

                // Apply the actual mappings from database
                if (PageRoleMappings.Any())
                {
                    foreach (var page in PageUrls)
                    {
                        var mapping = PageRoleMappings.FirstOrDefault(rm =>
                            rm.PagePath.Equals(page.PagePath, StringComparison.OrdinalIgnoreCase) &&
                            rm.RoleId == selectedRole.RoleId &&
                            rm.OrganizationID == activeOrganization.OrganizationId);

                        // Only set to true if mapping exists and HasAccess is true
                        page.HasAccess = mapping != null && mapping.HasAccess;
                        page.IsModified = false;
                    }
                }

                UpdateSelectAllState();

                if (pageRoleMappingGrid != null)
                {
                    await pageRoleMappingGrid.Refresh();
                }
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError("Error loading page role mappings: {ErrorMessage}", ex.Message);
                Snackbar.Add(Localizer["Error loading page role mappings."], Severity.Error);
            }
        }

        private void UpdateSelectAllState()
        {
            if (PageUrls != null && PageUrls.Any())
            {
                selectAllChecked = PageUrls.All(p => p.HasAccess);
            }
            else
            {
                selectAllChecked = false;
            }
        }

        private void OnSelectAllChanged(ChangeEventArgs e)
        {
            bool isChecked = (bool)e.Value;
            selectAllChecked = isChecked;

            if (PageUrls != null)
            {
                foreach (var page in PageUrls)
                {
                    page.HasAccess = isChecked;
                    page.IsModified = true;
                }
            }
            StateHasChanged();
        }

        /// <summary>
        /// Handles the checkbox change event and marks the page role mapping as modified.
        /// </summary>
        /// <param name="pageRoleMapping">The page role mapping data.</param>
        private void OnCheckboxChanged(PageRoleMappingData pageRoleMapping)
        {
            pageRoleMapping.IsModified = true;
            UpdateSelectAllState();
            StateHasChanged(); 
        }

        /// <summary>
        /// Saves the selected page role mappings.
        /// </summary>
        private async Task SaveSelectedPageRoleMappings()
        {
            try
            {
                if (selectedRole == null)
                {
                    Logger.LogWarning("No role selected.");
                    Snackbar.Add(Localizer["No role selected."], Severity.Warning);
                    return;
                }

                var modifiedPages = PageUrls.Where(page => page.IsModified).ToList();

                if (!modifiedPages.Any())
                {
                    Logger.LogInformation("No changes to save.");
                    Snackbar.Add(Localizer["No changes to save."], Severity.Info);
                    return;
                }

                await SaveAccessChanges(modifiedPages);

                foreach (var page in modifiedPages)
                {
                    page.IsModified = false;
                }

                Logger.LogInformation("Page role mappings updated successfully.");
                Snackbar.Add(Localizer["Page role mappings saved successfully."], Severity.Success);

                if (pageRoleMappingGrid != null)
                {
                    await pageRoleMappingGrid.Refresh();
                }

                RoleMappingState.NotifyStateChanged();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError("Error saving page role mappings: {ErrorMessage}", ex.Message);
                Snackbar.Add(Localizer["Failed to save page role mappings."], Severity.Error);
            }
        }

        /// <summary>
        /// Cancels the changes and reverts checkboxes to their original state.
        /// </summary>
        private void CancelChanges()
        {
            if (selectedRole == null) return;

            foreach (var page in PageUrls)
            {
                var originalMapping = PageRoleMappings.FirstOrDefault(rm => rm.PagePath == page.PagePath);
                page.HasAccess = originalMapping?.HasAccess ?? false;
                page.IsModified = false;
            }
            Logger.LogInformation("Changes cancelled and reverted to original state.");
            UpdateSelectAllState();
            Snackbar.Add(Localizer["Changes discarded."], Severity.Info);
            StateHasChanged();
        }

        /// <summary>
        /// Saves the access changes for the modified pages.
        /// </summary>
        /// <param name="modifiedPages">The list of modified pages.</param>
        /// <summary>
        /// Saves the access changes for the modified pages.
        /// </summary>
        /// <param name="modifiedPages">The list of modified pages.</param>
        private async Task SaveAccessChanges(List<PageRoleMappingData> modifiedPages)
        {
            if (selectedRole == null || selectedOrganization == null)
            {
                return;
            }

            try
            {
                var newMappings = new List<PageRoleMappingData>();
                var existingMappingsToUpdate = new List<PageRoleMappingData>();

                // Prepare all operations
                foreach (var page in modifiedPages)
                {
                    var existingMapping = PageRoleMappings.FirstOrDefault(prm =>
                        prm.PagePath.Equals(page.PagePath, StringComparison.OrdinalIgnoreCase) &&
                        prm.RoleId == selectedRole.RoleId &&
                        prm.OrganizationID == selectedOrganization.OrganizationId);

                    if (existingMapping != null)
                    {
                        // Update existing mapping
                        existingMapping.HasAccess = page.HasAccess;
                        existingMapping.UpdatedDate = DateTime.Now;
                        existingMapping.UpdatedBy = Guid.Parse(User.id);
                        existingMappingsToUpdate.Add(existingMapping);
                    }
                    else if (page.HasAccess)
                    {
                        // Add new mapping only if HasAccess is true
                        var newMapping = new PageRoleMappingData
                        {
                            Id = Guid.NewGuid(),
                            PagePath = page.PagePath,
                            RoleId = selectedRole.RoleId,
                            RoleName = selectedRole.RoleName,
                            OrganizationID = selectedOrganization.OrganizationId,
                            CreatedBy = Guid.Parse(User.id),
                            CreatedDate = DateTime.Now,
                            IsActive = true,
                            HasAccess = page.HasAccess,
                            Subscription = Subscription
                        };
                        newMappings.Add(newMapping);
                    }
                }

                // Save all new mappings at once
                if (newMappings.Count > 0)
                {
                    await PageRoleMappingService.AddPageRoleMappingAsync(newMappings);
                    Logger.LogInformation($"Successfully added {newMappings.Count} page role mappings");
                }

                // Update all existing mappings at once
                if (existingMappingsToUpdate.Count > 0)
                {
                    await PageRoleMappingService.UpdatePageRoleMappingAsync(existingMappingsToUpdate);
                    Logger.LogInformation($"Successfully updated {existingMappingsToUpdate.Count} page role mappings");
                }

                // Refresh data using the same method as OnRoleChanged
                var refreshedMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(selectedRole.RoleId, selectedOrganization.OrganizationId, Subscription);
                PageRoleMappings = refreshedMappings.ToList();

                StateHasChanged();

                Logger.LogInformation($"Successfully processed {modifiedPages.Count} page role mappings - {newMappings.Count} added, {existingMappingsToUpdate.Count} updated");
            }
            catch (Exception ex)
            {
                Logger.LogError("Error saving PageRoleMapping changes: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Fetch the country dropdown 
        /// </summary>
        private List<Country> _allCountries = new();

        private async Task<IEnumerable<string>> SearchCountries(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return new List<string>();


            if (_allCountries.Count == 0)
            {
                try
                {
                    _allCountries = (await CountryService.GetAllCountriesAsync()).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching countries: {ex.Message}");
                    return new List<string>();
                }
            }

            return _allCountries
                .Where(c => c.CountryName.StartsWith(value, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.CountryName)
                .ToList();
        }
    }
}
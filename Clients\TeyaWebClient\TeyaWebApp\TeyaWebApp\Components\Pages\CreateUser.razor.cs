﻿using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;
using DotNetEnv;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.AspNetCore.Components.Web;

using System.Text.RegularExpressions;
using TeyaWebApp.Components.Templates;
using System.Text.Json;
using Azure;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class CreateUser : ComponentBase
    {
        bool success;
        string[] errors = { };
        MudTextField<string> pwField1;
        MudTextField<string> usernameField;

        public CreateUser() => Env.Load();

        private bool isCreating = false;
        private string? createUserMessage = null;
        private int DelayMilliseconds, NoResultsCount, FirstIndex;
        private string signInType, issuer;
        private string? _selectedOrganization;
        private MudForm form;
        private string emailErrorMessage;
        public Member member = new Member();
        private readonly RazorComponentRenderer _razorComponentRenderer;
        private const int DefaultPasswordLength = 12;
        private bool Subscription = false;
        // Add this parameter to your dialog component class
        [Parameter]
        public EventCallback OnMemberUpdated { get; set; }


        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private ILogger<CreateUser> Logger { get; set; }
        [CascadingParameter] private MudDialogInstance MudDialog { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Inject] private IRoleService RoleService { get; set; }
        [Inject] private GraphApiService customAuthenticationService { get; set; }
        [Inject] private ICommunicationService CommunicationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private ICountryService countryService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Parameter] public Member? Member { get; set; }
        public string resultId { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private string id { get; set; }
        private bool _isRoleNameDisabled = false;


        private MudForm? formm;
        private bool isFormValid = false;
       

        public enum ExcludedRoles
        {
            Patient
        }

        protected override async Task OnInitializedAsync()
        {
            DelayMilliseconds = int.TryParse(Localizer["DelayMilliseconds"], out var delay) ? delay : 500;
            NoResultsCount = int.TryParse(Localizer["NoResultsCount"], out var noResults) ? noResults : 0;
            FirstIndex = int.TryParse(Localizer["FirstIndex"], out var firstIndex) ? firstIndex : 0;
            signInType = Localizer["emailAddress"];
            issuer = Environment.GetEnvironmentVariable("PrimaryDomain") ?? string.Empty;
            id = ActiveUser.id;
            member.OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(ActiveUser.OrganizationName);
            activeUserOrganizationId = member.OrganizationID ?? Guid.Empty;
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == "Enterprise";

            member.OrganizationName = ActiveUser.OrganizationName;
            _selectedOrganization = member.OrganizationName;

            if (Member != null)
            {
                member = Member;
                _isRoleNameDisabled = true;
            }
        }

        private string? emailValidationError = null;
        private bool isEmailValid = true;

        private async Task ValidateEmail(FocusEventArgs e)
        {
            emailValidationError = null;
            var email = member.Email?.Trim() ?? string.Empty;
            isEmailValid = true;

            if (string.IsNullOrWhiteSpace(email))
            {
                emailValidationError = "Email is required.";
                isEmailValid=false;
            }
            else if (!Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$"))
            {
                emailValidationError = "Invalid email format.";
                isEmailValid = false;
            }
            else if (!Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.(com|org|net|edu|gov|in|us|co|io|dev)$", RegexOptions.IgnoreCase))
            {
                emailValidationError = "Invalid email domain.";
                isEmailValid = false;
            }
            else
            {
                var exists = await MemberService.SearchMembersEmailAsync(email, activeUserOrganizationId, Subscription);
                if (exists)
                {
                    emailValidationError = "Email already exists.";
                    isEmailValid = false;
                }
            }
        }

        private IEnumerable<string> DEAValidation(string deaNumber)
        {
            var errors = new List<string>();
    
            if (!string.IsNullOrWhiteSpace(deaNumber))
            {
                if (!Regex.IsMatch(deaNumber, @"^[ABFGMYPRX][A-Z9]\d{7}$"))
                {
                    errors.Add(Localizer["DEAInvalidFormat"]); 
                }
            }   
    
            return errors;
        }

        private IEnumerable<string> AdditionalInfoValidation(string additionalInfo)
        {
            var errors = new List<string>();

            if (!string.IsNullOrWhiteSpace(additionalInfo))
            {
                if (additionalInfo.Length < 5)
                {
                    errors.Add(Localizer["AdditionalInfoMinLength"]);
                }
                else if (additionalInfo.Length > 300)
                {
                    errors.Add(Localizer["AdditionalInfoMaxLength"]);
                }
            }

            return errors;
        }

        private IEnumerable<string> JobDescriptionValidation(string jobDescription)
        {
            var errors = new List<string>();

            if (!string.IsNullOrWhiteSpace(jobDescription))
            {
                if (jobDescription.Length < 10)
                {
                    errors.Add(Localizer["JobDescriptionMinLength"]);
                }
                else if (jobDescription.Length > 200)
                {
                    errors.Add(Localizer["JobDescriptionMaxLength"]);
                }
            }

            return errors;
        }

        private IEnumerable<string> UsernameValidation(string username)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(username))
            {
                errors.Add(Localizer["UsernameRequired"]);
            }
            else if (!Regex.IsMatch(username, @"^[a-zA-Z0-9_.-]+$"))
            {
                errors.Add(Localizer["UsernameInvalidCharacters"]);
            }
            else if (Regex.IsMatch(username, @"\s"))
            {
                errors.Add(Localizer["UsernameNoSpaces"]);
            }

            return errors;
        }


        private string? ValidateAlphabetic(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' '))
                {
                    result = "Invalid input. Only alphabetic characters and spaces are allowed.";
                }
            }

            return result;
        }

       

        private IEnumerable<string> UPINValidation(string upin)
        {
            var errors = new List<string>();

            if (!string.IsNullOrWhiteSpace(upin))
            {
                if (!Regex.IsMatch(upin, @"^[A-Za-z]{5}\d{1}$"))
                {
                    errors.Add(Localizer["UPINInvalidFormat"]);
                }
            }

            return errors;
        }

        private IEnumerable<string> TaxIdValidation(string taxId)
        {
            var errors = new List<string>();

            if (!string.IsNullOrWhiteSpace(taxId))
            {
                if (!Regex.IsMatch(taxId, @"^\d{9}$"))
                {
                    errors.Add(Localizer["TaxIdInvalidFormat"]);
                }
            }

            return errors;
        }



        private async Task<IEnumerable<string>> SearchRoles(string searchTerm, CancellationToken cancellationToken)
        {
            var roleNames = new List<string>();
            try
            {
                var activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(ActiveUser.OrganizationName);
                member.OrganizationID = activeUserOrganizationId;
                member.OrganizationName = ActiveUser.OrganizationName;
                var roles = await RoleService.GetAllRolesByOrgIdAsync(activeUserOrganizationId, Subscription);

                roleNames = roles
                    .Where(role => role.IsActive &&
                          !string.Equals(role.RoleName, ExcludedRoles.Patient.ToString(), StringComparison.OrdinalIgnoreCase))
                    .Select(role => role.RoleName)
                    .ToList();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    roleNames = roleNames
                        .Where(name => name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingRoles"]);
            }
            return roleNames;
        }

        private async Task<IEnumerable<string>> SearchOrganizations(string searchTerm, CancellationToken cancellationToken)
        {
            var organizationNames = new List<string>();

            try
            {
                var organizations = await OrganizationService.GetAllOrganizationsAsync();
                organizationNames = organizations.Select(org => org.OrganizationName).ToList();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    organizationNames = organizationNames
                        .Where(name => name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingOrganizations"]);
            }

            return organizationNames;
        }


        public async Task<string> RenderComponentHtml(string email, string password)
        {
            try
            {
                inviteMailParametersService.Email = email;
                inviteMailParametersService.Password = password;
                string renderedHtml = await RazorRenderer.RenderComponentToHtmlAsync<InviteMail>();
                return renderedHtml;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["RenderError"]);
                return string.Empty;
            }
        }


        private async Task Save()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(member.UserName) ||
                    string.IsNullOrWhiteSpace(member.Email) ||
                    string.IsNullOrWhiteSpace(member.FirstName) ||
                    string.IsNullOrWhiteSpace(member.LastName) ||
                    string.IsNullOrWhiteSpace(member.Country))
                {
                    Snackbar.Add(Localizer["Please Fill All Required Fields"], Severity.Warning);
                    return;
                }

                if (!isEmailValid)
                {
                    Snackbar.Add(Localizer["Please Check the Email."], Severity.Error);
                    return;
                }

                if (form is null)
                    return;

                
                await form.Validate();

                if (!form.IsValid)
                {
                    Snackbar.Add(Localizer["Please fix validation errors before submitting."], Severity.Error);
                    return;
                }
                member.IsActive = true;
                if (!string.IsNullOrWhiteSpace(member.AccessControl))
                {
                    var roles = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
                    roles = roles.Where(role => role.RoleName == member.AccessControl).ToList();
                    if (roles != null && roles.Count > NoResultsCount)
                    {
                        member.RoleID = roles.First().RoleId;
                        member.RoleName = member.AccessControl;
                    }
                    else
                    {
                        Logger.LogWarning(Localizer["NoRolesFoundForAccessControl"]);
                        return;
                    }
                }

                if (Member == null)
                {
                    // Creating a new user             
                    bool mail_sent = await SendMailToUser();
                    if (mail_sent)
                    {
                        member.Id = Guid.TryParse(resultId, out Guid idCreated) ? idCreated : Guid.Empty;
                        var roleSelected = member.AccessControl;
                        if (roleSelected != null)
                        {
                            var roleModel = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
                            roleModel = roleModel.Where(role => role.RoleName == member.AccessControl).ToList();
                            member.RoleID = roleModel.FirstOrDefault()?.RoleId;
                            member.RoleName = roleSelected;
                        }

                        await MemberService.RegisterMembersAsync(new List<Member> { member });

                        // Update the parent component's member list using cascading parameter
                        if (OnMemberUpdated.HasDelegate)
                        {
                            await OnMemberUpdated.InvokeAsync();
                        }

                        // Show success message for new member creation                 
                        Snackbar.Add(Localizer["MemberCreatedSuccessfully"], Severity.Success);
                        StateHasChanged();
                        MudDialog.Close(DialogResult.Ok(true));
                    }
                    else
                    {
                        Snackbar.Add(Localizer["ErrorCreatingMember"], Severity.Error);
                    }
                }
                else
                {
                    // Updating an existing user             
                    try
                    {
                        await MemberService.UpdateMemberByIdAsync(member.Id, member);

                        // Update the parent component's member list using cascading parameter
                        if (OnMemberUpdated.HasDelegate)
                        {
                            await OnMemberUpdated.InvokeAsync();
                        }

                        // Show success message for member update                 
                        Snackbar.Add(Localizer["MemberUpdatedSuccessfully"], Severity.Success);
                        MudDialog.Close(DialogResult.Ok(true));
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, Localizer["ErrorUpdatingMember"]);
                        Snackbar.Add(Localizer["ErrorUpdatingMember"], Severity.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSavingMember"]);
                Snackbar.Add(Localizer["ErrorSavingMember"], Severity.Error);
            }
        }

        private void Cancel()
        {
            MudDialog.Cancel();
        }

        private async Task<bool> SendMailToUser()
        {
            bool answer = false;
            try
            {
                isCreating = true;
                createUserMessage = null;

                string password = GenerateRandomPassword();

                resultId = await customAuthenticationService.CreateUserAsync2(
                    member.UserName, member.Email, password, signInType, issuer, member.Email, member.AccessControl);


                var updateFields = new Dictionary<string, object>
                {
                    { "displayName", member.UserName },
                    { $"{Environment.GetEnvironmentVariable("EXTENSION-PREFIX")}_OrganizationName",member.OrganizationName},
                    { "Country",member.Country},
                    { "GivenName",member.FirstName },
                    { "SurName",member.LastName },
                };

                bool updateSuccessful = await customAuthenticationService.UpdateUserProfileAsync(resultId, updateFields);
                if (!updateSuccessful)
                {
                    Logger.LogError(Localizer["FailedToUpdateDisplayName"]);
                }



                if (resultId != null)
                {
                    createUserMessage = Localizer["UserCreated"];
                    NavigationManager.NavigateTo(Localizer["NavigateToUserManagementPage"]);

                    var recipientList = new List<string> { member.Email };
                    try
                    {
                        var response = await RenderComponentHtml(member.Email, password);
                        var sendMail = await CommunicationService.SendHtmlEmailService(
                            Localizer["EmailSenderAddress"], Localizer["EmailSubject"], response, recipientList);

                        if (sendMail.HasCompleted)
                        {
                            answer = true;
                            Logger.LogInformation(Localizer["EmailCreatedSuccessful"]);
                        }
                        else
                        {
                            answer = false;
                            Logger.LogWarning(Localizer["EmailPendingOrFailedMessage"]);
                        }
                    }
                    catch (Exception emailEx)
                    {
                        answer = false;
                        Logger.LogError(emailEx, Localizer["EmailErrorMessage"]);
                    }
                }
                else
                {
                    answer = false;
                    createUserMessage = Localizer["UserCreationFailed"];
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorCreatingUser", "An error occurred while creating the user"]);
                createUserMessage = string.Format(Localizer["ErrorCreatingUser"], ex.Message);
            }
            finally
            {
                isCreating = false;
            }
            return answer;
        }
        private string GenerateRandomPassword(int length = DefaultPasswordLength)
        {
            string chars = Localizer["RandomPasswordChars"];

            Random random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                                         .Select(charSet => charSet[random.Next(charSet.Length)]).ToArray());
        }


        /// <summary>
        /// Fetch the country dropdown 
        /// </summary>
        private List<Country> _allCountries = new();
       
        private async Task<IEnumerable<string>> SearchCountries(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return new List<string>();


            if (_allCountries.Count == 0)
            {
                try
                {
                    _allCountries = (await countryService.GetAllCountriesAsync()).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching countries: {ex.Message}");
                    return new List<string>();
                }
            }

            return _allCountries
                .Where(c => c.CountryName.StartsWith(value, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.CountryName)
                .ToList();
        }

    }
}

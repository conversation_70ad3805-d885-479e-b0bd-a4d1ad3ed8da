@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<div class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <div class="cosigning-header">
                <div class="header-content">
                    <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                             Color="Color.Primary"
                             Size="Size.Medium"
                             Class="header-icon" />
                    <MudText Typo="Typo.h6" Class="header-title">
                        @Localizer["DocumentSignature"]
                    </MudText>
                </div>
                <MudChip Size="Size.Small"
                         Color="@GetStatusColor()"
                         Variant="Variant.Filled"
                         Class="status-chip">
                    @GetStatusText()
                </MudChip>
            </div>

            <!-- Patient Information Section -->
            <div class="patient-info-section">
                <div class="patient-details">
                    <div class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.Person"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["Patient"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @PatientName
                        </MudText>
                    </div>
                    <div class="patient-info-row">
                        <MudIcon Icon="@Icons.Material.Filled.CalendarToday"
                                 Color="Color.Secondary"
                                 Size="Size.Small" />
                        <MudText Typo="Typo.body2" Class="info-label">
                            @Localizer["Date"]:
                        </MudText>
                        <MudText Typo="Typo.body2" Class="info-value">
                            @DateTime.Now.ToString("MMM dd, yyyy")
                        </MudText>
                    </div>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="signature-section">
                @if (!CurrentCosigning.IsSigned)
                {
                    <!-- Primary Signature -->
                    <div class="signature-block">
                        <div class="signature-header">
                            <MudText Typo="Typo.subtitle2" Class="signature-title">
                                @Localizer["PrimarySignature"]
                            </MudText>
                        </div>
                        <div class="signature-actions">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Draw"
                                       OnClick="SignDocument"
                                       Disabled="@IsProcessing"
                                       Class="signature-button">
                                @if (IsProcessing)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    <span class="ml-2">@Localizer["Signing"]...</span>
                                }
                                else
                                {
                                    @Localizer["SignDocument"]
                                }
                            </MudButton>
                        </div>
                    </div>
                }
                else
                {
                    <!-- Signed Status -->
                    <div class="signature-block signed">
                        <div class="signature-header">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                     Color="Color.Success"
                                     Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                @Localizer["SignedBy"]: @CurrentCosigning.SignerName
                            </MudText>
                        </div>
                        <MudText Typo="Typo.caption" Class="signature-timestamp">
                            @CurrentCosigning.Date?.ToString("MMM dd, yyyy 'at' h:mm tt")
                        </MudText>
                    </div>

                    @if (RequiresCosignature && !CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosignature Section -->
                        <MudDivider Class="signature-divider" />
                        <div class="signature-block">
                            <div class="signature-header">
                                <MudText Typo="Typo.subtitle2" Class="signature-title">
                                    @Localizer["RequiredCosignature"]
                                </MudText>
                            </div>
                            <div class="signature-actions">
                                <MudButton Variant="Variant.Outlined"
                                           Color="Color.Secondary"
                                           StartIcon="@Icons.Material.Filled.SupervisorAccount"
                                           OnClick="CosignDocument"
                                           Disabled="@IsProcessing"
                                           Class="signature-button">
                                    @if (IsProcessing)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        <span class="ml-2">@Localizer["Cosigning"]...</span>
                                    }
                                    else
                                    {
                                        @Localizer["Cosign"]
                                    }
                                </MudButton>
                            </div>
                        </div>
                    }
                    else if (CurrentCosigning.IsCosigned)
                    {
                        <!-- Cosigned Status -->
                        <MudDivider Class="signature-divider" />
                        <div class="signature-block signed">
                            <div class="signature-header">
                                <MudIcon Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success"
                                         Size="Size.Small" />
                                <MudText Typo="Typo.subtitle2" Class="signature-title signed">
                                    @Localizer["CosignedBy"]: @CurrentCosigning.CosignerName
                                </MudText>
                            </div>
                            <MudText Typo="Typo.caption" Class="signature-timestamp">
                                @CurrentCosigning.LastUpdated?.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </MudText>
                        </div>
                    }
                }
            </div>

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <div class="lock-status">
                    <MudAlert Severity="Severity.Info"
                              Variant="Variant.Filled"
                              Dense="true"
                              Class="lock-alert">
                        <div class="lock-content">
                            <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                            <span>@Localizer["DocumentLocked"]</span>
                        </div>
                    </MudAlert>
                </div>
            }
        </MudPaper>
    }
</div>



﻿@page "/preventivemedicine"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns


<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" @onclick="OpenPreventiveMedicineDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_preventiveMedicineDialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["PreventiveMedicine"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <!-- Category Dropdown with Search -->

                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMCategory" @bind-Value="SelectedCategory"
                                        DataSource="@Categories"
                                        Placeholder="Select Category"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <AutoCompleteFieldSettings Value="PMCategoryName" Text="PMCategoryName"></AutoCompleteFieldSettings>
                            <AutoCompleteEvents TValue="string" TItem="PMCategory"
                                                ValueChange="@OnCategoryChange" />
                        </SfAutoComplete>
                    </MudItem>

                    <!-- SubCategory Dropdown with Search -->
                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMSubCategory" @bind-Value="SelectedSubCategory"
                                        DataSource="@FilteredSubCategories"
                                        Placeholder="Select Sub-Category"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Enabled="@(!string.IsNullOrEmpty(SelectedCategory))">
                            <AutoCompleteFieldSettings Value="PMSubcategoryName" Text="PMSubcategoryName"></AutoCompleteFieldSettings>
                            <AutoCompleteEvents TValue="string" TItem="PMSubCategory"
                                                ValueChange="@OnSubCategoryChange" />
                        </SfAutoComplete>
                    </MudItem>

                    <!-- Symptoms Dropdown with Search -->
                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMSymptoms" @bind-Value="SelectedSymptom"
                                        DataSource="@FilteredSymptoms"
                                        Placeholder="Symptom"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Enabled="@(!string.IsNullOrEmpty(SelectedSubCategory))">
                                        
                            <AutoCompleteFieldSettings Value="PMSymptomName" Text="PMSymptomName"></AutoCompleteFieldSettings>
                        </SfAutoComplete>
                    </MudItem>

                    <!-- Add Button -->
                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;margin-top: 4px;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewEntry"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="height:37px;width:80px;padding: 2px 16px;font-size: 0.8rem;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- Preventive Medicines Grid -->
                <SfGrid @ref="PreventiveMedicineGrid"
                        TValue="PreventiveMedicines"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@preventiveMedicineEntries"
                        AllowPaging="true"
                        GridLines="GridLine.Both"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="PreventiveMedicines"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="PreventiveMedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" Width="130" Format="MM-dd-yyyy"></GridColumn>
                        <GridColumn Field="Category" HeaderText="@Localizer["Category"]" TextAlign="TextAlign.Center" Width="130"></GridColumn>
                        <GridColumn Field="SubCategory" HeaderText="@Localizer["SubCategory"]" TextAlign="TextAlign.Center" Width="130"></GridColumn>
                        <GridColumn Field="Symptoms" HeaderText="@Localizer["Symptoms"]" TextAlign="TextAlign.Center" Width="130"></GridColumn>
                        <GridColumn Field="Detection" HeaderText="@Localizer["Detection"]" TextAlign="TextAlign.Center" Width="140"></GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="70">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 100px; height: 35px; font-weight: 600;padding: 2px 16px;font-size: 0.8rem;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 100px; height: 35px; font-weight: 600;padding: 2px 16px;font-size: 0.8rem;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>


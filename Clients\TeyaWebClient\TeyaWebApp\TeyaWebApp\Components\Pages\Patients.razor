﻿@page "/patients"
@page "/patients/{memberId:guid?}"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "patientsAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using MudBlazor.Extensions
@using Syncfusion.Blazor.Navigations
@using System.Text.RegularExpressions
@layout Admin
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject IMemberService MemberService
@inject IAddressService AddressService
@inject IInsuranceService InsuranceService
@inject IGuardianService GuardianService
@inject IEmployerService EmployerService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject InviteMailParametersService inviteMailParametersService
@inject RazorComponentRenderer RazorRenderer


<div class="patient-management-container">
    <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-6">



        <!-- Patient Form Stepper -->
        <div class="patient-stepper-section">
            <SfStepper Linear="true" ID="patient-stepper" @ref="PatientStepper" StepChanging="@HandleStepChange">
                <StepperSteps>
                    <StepperStep @ref="PersonalInfoStep" IconCss="fas fa-user" Text="Personal Information" />
                    <StepperStep @ref="GuardianStep" IconCss="fas fa-user-shield" Text="Guardian" />
                    <StepperStep @ref="EmployerStep" IconCss="fas fa-briefcase" Text="Employment" />
                    <StepperStep @ref="DemographicsStep" IconCss="fas fa-id-card" Text="Stats Details" />
                    <StepperStep @ref="InsuranceStep" IconCss="fas fa-file-invoice-dollar" Text="Insurance" />
                    <StepperStep @ref="AddressStep" IconCss="fas fa-map-marker-alt" Text="Address" />
                    <StepperStep @ref="MiscStep" IconCss="fas fa-ellipsis-h" Text="Miscellaneous" />
                </StepperSteps>
            </SfStepper>

        </div>



        <div id="patient-step-content">

            <MudForm @ref="personalForm">
                <MudGrid>
                    <div id="personal-info-container" class="step-content @PersonalInfoContainerClass">
                        <MudCard Elevation="2" Class="member-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.Person" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Personal Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">


                                <div class="basic-info-section">
                                    <MudText Typo="Typo.subtitle1" Class="section-title mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Class="mr-2" />
                                        Basic Information
                                    </MudText>

                                    <MudGrid Spacing="3">
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.UserName"
                                                          Label="@Localizer["UserName"]"
                                                          Required="true"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.Email"
                                                          Label="@Localizer["Email"]"
                                                          Required="true"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield"
                                                          OnBlur="@ValidateAndCheckEmailAsync"
                                                          Error="@emailExists"
                                                          ErrorText="@emailErrorText" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.PhoneNumber"
                                                          Label="@Localizer["PhoneNumber"]"
                                                          Validation="ValidatePhoneNumber"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.FirstName"
                                                          Label="@Localizer["FirstName"]"
                                                          Required="true"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.LastName"
                                                          Label="@Localizer["LastName"]"
                                                          Required="true"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.PreferredName"
                                                          Label="@Localizer["PreferredName"]"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudDatePicker @ref="dobDatePicker"
                                                           Label="@Localizer["DOB"]"
                                                           MaxDate="DateTime.Today"
                                                           Clearable="true"
                                                           AutoClose="true"
                                                           DateFormat="MM/dd/yyyy"
                                                           Variant="Variant.Outlined"
                                                           MinDate="DateTime.Today.AddYears(-200)"
                                                           Class="modern-datepicker"
                                                           PickerVariant="PickerVariant.Inline"
                                                           @bind-Date="member.DateOfBirth">
                                                <PickerActions>
                                                    <div style="position: absolute; top: 20px; right: 20px;">
                                                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                       OnClick="@(() => CloseDatePicker(dobDatePicker))"
                                                                       Size="Size.Small"
                                                                       Style="color: white !important" />
                                                    </div>
                                                </PickerActions>
                                            </MudDatePicker>
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudAutocomplete T="string"
                                                             Required="true"
                                                             Label="@Localizer["Country"]"
                                                             Value="@member.Country"
                                                             ValueChanged="@(value => member.Country = value)"
                                                             SearchFunc="SearchCountries"
                                                             Clearable="true"
                                                             Variant="Variant.Outlined"
                                                             Class="modern-autocomplete"
                                                             Placeholder="Search Country" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.ExternalID"
                                                          Label="@Localizer["ExternalID"]"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </div>

                                <!-- Additional Information -->
                                <div class="additional-info-section mt-4">
                                    <MudText Typo="Typo.subtitle1" Class="section-title mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.MoreHoriz" Size="Size.Small" Class="mr-2" />
                                        Additional Information
                                    </MudText>

                                    <MudGrid Spacing="3">
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.MaritalStatus"
                                                          Label="@Localizer["MaritalStatus"]"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.SexualOrientation"
                                                          Label="@Localizer["SexualOrientation"]"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.PreviousNames"
                                                          Label="@Localizer["PreviousNames"]"
                                                          Validation="ValidateAlphabetic"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>

                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="member.SSN"
                                                          Label="@Localizer["SSN"]"
                                                          Placeholder="e.g. ***********"
                                                          Variant="Variant.Outlined"
                                                          InputType="InputType.Text"
                                                          Class="modern-textfield"
                                                          Clearable="true"
                                                          Immediate="false"
                                                          OnBlur="@OnMemberSSNBlur"
                                                          Error="@ssnExistsMember"
                                                          ErrorText="@ssnErrorTextMember" />
                                        </MudItem>
                                    </MudGrid>
                                </div>
                                <div class="photo-upload-section mt-4">
                                    <MudText Typo="Typo.subtitle1" Class="section-title mb-3">
                                        <MudIcon Icon="@Icons.Material.Filled.PhotoCamera" Size="Size.Small" Class="mr-2" />
                                        Patient Photo
                                    </MudText>

                                    @if (string.IsNullOrEmpty(member.ProfileImageUrl))
                                    {
                                        <div class="upload-area">
                                            <MudFileUpload T="IBrowserFile" FilesChanged="@(file => HandleFileUploadWrapper(file))">
                                                <ActivatorContent>
                                                    <div class="upload-dropzone">
                                                        <MudIcon Icon="@Icons.Material.Filled.CloudUpload" Size="Size.Large" Class="upload-icon mb-2" />
                                                        <MudText Typo="Typo.body1" Class="mb-1">Click to upload patient photo</MudText>
                                                        <MudText Typo="Typo.caption" Class="text-muted">Supported formats: JPG, PNG, GIF (Max 10MB)</MudText>
                                                    </div>
                                                </ActivatorContent>
                                            </MudFileUpload>
                                            @if (!string.IsNullOrEmpty(uploadedFileName))
                                            {
                                                <MudText Typo="Typo.caption" Class="mt-2 uploaded-filename">
                                                    <MudIcon Icon="@Icons.Material.Filled.AttachFile" Size="Size.Small" Class="mr-1" />
                                                    @uploadedFileName
                                                </MudText>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="uploaded-photo-section">
                                            <div class="photo-status">
                                                <MudChip T="string"
                                                         Icon="@Icons.Material.Filled.CheckCircle"
                                                         Color="Color.Success"
                                                         Size="Size.Medium"
                                                         Class="success-chip">
                                                    @Localizer["PatientPhotoUploaded"]
                                                </MudChip>
                                                <MudTooltip Text="Remove Image">
                                                    <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                                   Color="Color.Error"
                                                                   Size="Size.Small"
                                                                   OnClick="RemovePatientPhoto"
                                                                   Class="remove-photo-btn" />
                                                </MudTooltip>
                                            </div>

                                            @if (!string.IsNullOrEmpty(uploadedFileName))
                                            {
                                                <MudButton Variant="Variant.Text"
                                                           StartIcon="@Icons.Material.Filled.Visibility"
                                                           OnClick="TogglePreview"
                                                           Class="preview-btn mt-2">
                                                    @(showPreview ? "Hide Preview" : "Show Preview")
                                                </MudButton>
                                            }

                                            @if (showPreview && !string.IsNullOrEmpty(imagePreviewUrl))
                                            {
                                                <div class="image-preview mt-3">
                                                    <img src="@imagePreviewUrl" alt="Patient Preview" class="preview-image" />
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4">
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>

                </MudGrid>
            </MudForm>
            <MudForm @ref="guardianForm">
                <MudGrid>
                    <!-- Guardian Information Step -->
                    <div id="guardian-container" class="step-content @GuardianContainerClass">
                        <MudCard Elevation="2" Class="guardian-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.FamilyRestroom" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Guardian Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianName"
                                                      Label="@Localizer["GuardianName"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianRelationship"
                                                      Label="@Localizer["GuardianRelationship"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianSex"
                                                      Label="@Localizer["GuardianSex"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianPhone"
                                                      Label="@Localizer["GuardianPhone"]"
                                                      Validation="ValidatePhoneNumber"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianEmail"
                                                      Label="@Localizer["GuardianEmail"]"
                                                      OnBlur="@ValidateAndCheckEmailAsync"
                                                      Error="@emailExists"
                                                      ErrorText="@emailErrorText"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianSSIN"
                                                      Label="@Localizer["GuardianSSIN"]"
                                                      Placeholder="e.g. ***********"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield"
                                                      InputType="InputType.Text"
                                                      Clearable="true"
                                                      OnBlur="@OnGuardianSSNBlur"
                                                      Error="@ssnExistsGuardian"
                                                      ErrorText="@ssnErrorTextGuardian" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianAddress"
                                                      Label="@Localizer["GuardianAddress"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianCity"
                                                      Label="@Localizer["GuardianCity"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="guardian.GuardianState"
                                                      Label="@Localizer["GuardianState"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudAutocomplete T="string"
                                                         Label="@Localizer["GuardianCountry"]"
                                                         Value="@guardian.GuardianCountry"
                                                         ValueChanged="@(value => guardian.GuardianCountry = value)"
                                                         SearchFunc="SearchCountries"
                                                         Clearable="true"
                                                         Variant="Variant.Outlined"
                                                         Class="modern-autocomplete"
                                                         Placeholder="Search Country" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>
                </MudGrid>
            </MudForm>
                    
                   
            <MudForm @ref="employmentForm">
                <MudGrid>
                    <!-- Employment Information Step -->
                    <div id="employment-container" class="step-content @EmploymentContainerClass">
                        <MudCard Elevation="2" Class="employer-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.Work" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Employment Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.Occupation"
                                                      Label="@Localizer["Occupation"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerName"
                                                      Label="@Localizer["EmployerName"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerIndustry"
                                                      Label="@Localizer["Industry"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerAddress"
                                                      Label="@Localizer["EmployerAddress"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerAddressLine2"
                                                      Label="@Localizer["EmployerAddressLine2"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerCity"
                                                      Label="@Localizer["EmployerCity"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerState"
                                                      Label="@Localizer["EmployerState"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="employer.EmployerPostalCode"
                                                      Label="@Localizer["PostalCode"]"
                                                      Validation="ValidatePostalCode"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudAutocomplete T="string"
                                                         Label="@Localizer["EmployerCountry"]"
                                                         Value="@employer.EmployerCountry"
                                                         ValueChanged="@(value => employer.EmployerCountry = value)"
                                                         SearchFunc="SearchCountries"
                                                         Clearable="true"
                                                         Variant="Variant.Outlined"
                                                         Class="modern-autocomplete"
                                                         Placeholder="Search Country" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>
                </MudGrid>
            </MudForm>
           

            <MudForm @ref="demographicsForm">
                <MudGrid>
                    <!-- Demographics Step -->
                    <div id="demographics-container" class="step-content @DemographicsContainerClass">
                        <MudCard Elevation="2" Class="stats-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.Analytics" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title"> Stats Details</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.Language"
                                                      Label="@Localizer["Language"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.Ethnicity"
                                                      Label="@Localizer["Ethnicity"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.Race"
                                                      Label="@Localizer["Race"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.Nationality"
                                                      Label="@Localizer["Nationality"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.Religion"
                                                      Label="@Localizer["Religion"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.FamilySize"
                                                      Label="@Localizer["FamilySize"]"
                                                      Validation="ValidateNumeric"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.MonthlyIncome"
                                                      Label="@Localizer["MonthlyIncome"]"
                                                      Validation="ValidateDecimal"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="member.ReferralSource"
                                                      Label="@Localizer["ReferralSource"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudDatePicker @ref="financialReviewDatePicker"
                                                       Label="@Localizer["FinancialReviewDate"]"
                                                       MaxDate="DateTime.Today"
                                                       Variant="Variant.Outlined"
                                                       MinDate="DateTime.Today.AddYears(-200)"
                                                       Class="modern-datepicker"
                                                       Clearable="true"
                                                       PickerVariant="PickerVariant.Inline"
                                                       @bind-Date="member.FinancialReviewDate"
                                                       AutoClose="true">
                                            <PickerActions>
                                                <div style="position: absolute; top: 20px; right: 20px;">
                                                    <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                   OnClick="@(() => CloseDatePicker(financialReviewDatePicker))"
                                                                   Size="Size.Small"
                                                                   Style="color: white !important" />
                                                </div>
                                            </PickerActions>
                                        </MudDatePicker>
                                    </MudItem>
                                </MudGrid>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>

                </MudGrid>
            </MudForm>
           
            <MudForm @ref="insuranceForm">
                <MudGrid>
                    <!-- Insurance Information Step -->
                    <div id="insurance-container" class="step-content @InsuranceContainerClass">
                        <MudCard Elevation="2" Class="insurance-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.HealthAndSafety" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Insurance Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.PrimaryInsuranceProvider"
                                                      Label="@Localizer["PrimaryInsuranceProvider"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.PlanName"
                                                      Label="@Localizer["PlanName"]"
                                                      Validation="ValidateAlphanumeric"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.Subscriber"
                                                      Label="@Localizer["Subscriber"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.PolicyNumber"
                                                      Label="@Localizer["PolicyNumber"]"
                                                      Validation="ValidateAlphanumeric"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.GroupNumber"
                                                      Label="@Localizer["GroupNumber"]"
                                                      Validation="ValidateAlphanumeric"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.CoPay"
                                                      Label="@Localizer["CoPay"]"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" 
                                                      Validation="ValidateDecimal" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudDatePicker @ref="effectiveDatePicker"
                                                       Label="@Localizer["EffectiveDate"]"
                                                       MaxDate="DateTime.Today"
                                                       Variant="Variant.Outlined"
                                                       MinDate="DateTime.Today.AddYears(-200)"
                                                       Class="modern-datepicker"
                                                       Clearable="true"
                                                       PickerVariant="PickerVariant.Inline"
                                                       @bind-Date="insurance.EffectiveDate"
                                                       AutoClose="true">
                                            <PickerActions>
                                                <div style="position: absolute; top: 20px; right: 20px;">
                                                    <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                   OnClick="@(() => CloseDatePicker(effectiveDatePicker))"
                                                                   Size="Size.Small"
                                                                   Style="color: white !important" />
                                                </div>
                                            </PickerActions>
                                        </MudDatePicker>
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.Relationship"
                                                      Label="@Localizer["Relationship"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.AcceptAssignment"
                                                      Label="@Localizer["AcceptAssignment"]"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Subscriber Information Section -->
                                <MudDivider Class="my-4" />
                                <MudText Typo="Typo.subtitle1" Class="section-title mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" />
                                    Subscriber Information
                                </MudText>

                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberEmployer"
                                                      Label="@Localizer["SubscriberEmployer"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.Sex"
                                                      Label="@Localizer["Sex"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberPhone"
                                                      Label="@Localizer["SubscriberPhone"]"
                                                      Validation="ValidatePhoneNumber"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberAddressLine1"
                                                      Label="@Localizer["SubscriberAddressLine1"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberAddressLine2"
                                                      Label="@Localizer["SubscriberAddressLine2"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberCity"
                                                      Label="@Localizer["SubscriberCity"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberState"
                                                      Label="@Localizer["SubscriberState"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="insurance.SubscriberZipCode"
                                                      Label="@Localizer["SubscriberZipCode"]"
                                                      Validation="ValidatePostalCode"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudAutocomplete T="string"
                                                         Label="@Localizer["SubscriberCountry"]"
                                                         Value="@insurance.SubscriberCountry"
                                                         ValueChanged="@(value => insurance.SubscriberCountry = value)"
                                                         SearchFunc="SearchCountries"
                                                         Clearable="true"
                                                         Variant="Variant.Outlined"
                                                         Class="modern-autocomplete"
                                                         Placeholder="Search Country" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>
                </MudGrid>
            </MudForm>
           

            <MudForm @ref="addressForm">
                <MudGrid>
                    <!-- Address Information Step -->
                    <div id="address-container" class="step-content @AddressContainerClass">
                        <MudCard Elevation="2" Class="address-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.LocationOn" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Address Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="6">
                                        <MudTextField @bind-Value="address.AddressLine1"
                                                      Label="@Localizer["AddressLine1"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudTextField @bind-Value="address.AddressLine2"
                                                      Label="@Localizer["AddressLine2"]"
                                                      Validation="ValidateAddress"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="address.City"
                                                      Label="@Localizer["City"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="address.State"
                                                      Label="@Localizer["State"]"
                                                      Validation="ValidateAlphabetic"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudTextField @bind-Value="address.PostalCode"
                                                      Label="@Localizer["PostalCode"]"
                                                      Validation="ValidatePostalCode"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                    <MudItem xs="12" md="4">
                                        <MudAutocomplete T="string"
                                                         Label="@Localizer["Country"]"
                                                         Value="@address.Country"
                                                         ValueChanged="@(value => address.Country = value)"
                                                         SearchFunc="SearchCountries"
                                                         Clearable="true"
                                                         Variant="Variant.Outlined"
                                                         Class="modern-autocomplete"
                                                         Placeholder="Search Country" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="@NextStep"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.NavigateNext"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        Next
                                    </MudButton>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>
                </MudGrid>
            </MudForm>
          

            <MudForm @ref="miscellaneousForm">
                <MudGrid>
                    <!-- Miscellaneous Information Step -->
                    <div id="miscellaneous-container" class="step-content @MiscellaneousContainerClass">
                        <MudCard Elevation="2" Class="miscellaneous-details-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <div class="card-header-content">
                                        <MudIcon Icon="@Icons.Material.Filled.MoreVert" Class="header-icon" />
                                        <MudText Typo="Typo.h6" Class="header-title">Miscellaneous Information</MudText>
                                    </div>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="card-content-padding">
                                <MudGrid Spacing="3">
                                    <MudItem xs="12" md="6">
                                        <MudDatePicker @ref="deceasedDatePicker"
                                                       Label="@Localizer["DeceasedDate"]"
                                                       MaxDate="DateTime.Today"
                                                       Variant="Variant.Outlined"
                                                       MinDate="DateTime.Today.AddYears(-200)"
                                                       Class="modern-datepicker"
                                                       Clearable="true"
                                                       PickerVariant="PickerVariant.Inline"
                                                       @bind-Date="member.DateDeceased"
                                                       AutoClose="true">
                                            <PickerActions>
                                                <div style="position: absolute; top: 20px; right: 20px;">
                                                    <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                   OnClick="@(() => CloseDatePicker(deceasedDatePicker))"
                                                                   Size="Size.Small"
                                                                   Style="color: white !important" />
                                                </div>
                                            </PickerActions>
                                        </MudDatePicker>
                                    </MudItem>
                                    <MudItem xs="12" md="6">
                                        <MudTextField @bind-Value="member.ReasonDeceased"
                                                      Label="@Localizer["DeceasedReason"]"
                                                      Variant="Variant.Outlined"
                                                      Class="modern-textfield" />
                                    </MudItem>
                                </MudGrid>

                                <!-- Final Step Navigation -->
                                <div class="step-navigation mt-4 d-flex gap-3">
                                    <MudButton Variant="Variant.Outlined"
                                               OnClick="@PreviousStep"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.NavigateBefore"
                                               Size="Size.Large"
                                               Class="step-button previous-button">
                                        Previous
                                    </MudButton>
                                    @if (isAddingNewMember)
                                    {
                                        <MudButton Variant="Variant.Filled"
                                                   OnClick="HandleSubmit"
                                                   Color="Color.Success"
                                                   StartIcon="@Icons.Material.Filled.PersonAdd"
                                                   Size="Size.Large"
                                                   Class="step-button submit-button">
                                            @Localizer["AddMember"]
                                        </MudButton>
                                    }
                                    else
                                    {
                                        <MudButton Variant="Variant.Outlined"
                                                   OnClick="HandleDelete"
                                                   Color="Color.Error"
                                                   StartIcon="@Icons.Material.Filled.Delete"
                                                   Size="Size.Large"
                                                   Class="step-button delete-button mr-3">
                                            @Localizer["Delete"]
                                        </MudButton>
                                        <MudButton Variant="Variant.Filled"
                                                   OnClick="HandleSubmit"
                                                   Color="Color.Success"
                                                   StartIcon="@Icons.Material.Filled.Save"
                                                   Size="Size.Large"
                                                   Class="step-button submit-button">
                                            @Localizer["Update"]
                                        </MudButton>
                                    }
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </div>
                </MudGrid>
            </MudForm>
        </div>
    </MudContainer>
</div>



<style>
    /* ===== STEPPER ICON FONTS ===== */
    @@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css');

    @@font -face {
        font-family: 'Patient-icons';
        src: url(data:application/x-font-ttf;charset=utf-8;base64,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) format('truetype');
        font-weight: normal;
        font-style: normal;
    }

    [class^="sf-icon-"],
    [class*=" sf-icon-"] {
        font-family: 'Patient-icons' !important;
        speak: none;
        font-size: 55px;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: inherit;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .sf-icon-personal-info:before {
        content: "\e700";
    }

    .sf-icon-guardian-info:before {
        content: "\e701";
    }

    .sf-icon-employer-info:before {
        content: "\e702";
    }

    .sf-icon-demographics-info:before {
        content: "\e703";
    }

    .sf-icon-insurance-info:before {
        content: "\e704";
    }

    .sf-icon-address-info:before {
        content: "\e705";
    }

    .sf-icon-misc-info:before {
        content: "\e706";
    }

    /* ===== MODERN PATIENT MANAGEMENT STYLES ===== */

    /* Container and Layout */
    .patient-management-container {
        min-height: 100vh;
        padding: 0;
    }

        /* Improved container spacing for wider layout */
        .patient-management-container .mud-container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }



    /* Stepper Styling */
    .patient-stepper-section {
        width: 90%;
        margin: 40px auto;
    }

    #patient-step-content {
        position: relative;
        width: 88%;
        min-height: 250px;
        margin: 0 auto;
    }

    .step-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        pointer-events: none;
    }

        /* Display the active step content */
        .step-content.step-active {
            animation: fadeInUp 0.4s;
            opacity: 1;
            z-index: 1000;
            pointer-events: all;
        }

    /* Keyframes */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            -webkit-transform: translate3d(0, 50%, 0);
            transform: translate3d(0, 50%, 0);
        }

        to {
            opacity: 1;
            -webkit-transform: none;
            transform: none;
        }
    }

    /* Card Styling */
    .member-details-card,
    .guardian-details-card,
    .employer-details-card,
    .stats-details-card,
    .insurance-details-card,
    .address-details-card,
    .miscellaneous-details-card,
    .action-buttons-card {
        border-radius: 16px;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    /* Card Headers */
    .card-header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .header-icon {
        color: #667eea;
        font-size: 1.5rem;
    }

    .header-title {
        font-weight: 600;
        margin: 0;
    }

    /* Card Content */
    .card-content-padding {
        padding: 2rem;
    }

    /* Form Elements */
    .modern-textfield,
    .modern-autocomplete,
    .modern-datepicker {
        margin-bottom: 0.5rem;
    }

        .modern-textfield ::deep .mud-input-root,
        .modern-autocomplete ::deep .mud-input-root,
        .modern-datepicker ::deep .mud-input-root {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

            .modern-textfield ::deep .mud-input-root:hover,
            .modern-autocomplete ::deep .mud-input-root:hover,
            .modern-datepicker ::deep .mud-input-root:hover {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

            .modern-textfield ::deep .mud-input-root.mud-input-root-focused,
            .modern-autocomplete ::deep .mud-input-root.mud-input-root-focused,
            .modern-datepicker ::deep .mud-input-root.mud-input-root-focused {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            }

        /* Date Picker Improvements */
        .modern-datepicker ::deep .mud-picker-input-button {
            border-radius: 0 12px 12px 0;
        }

        .modern-datepicker ::deep .mud-picker-popup {
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            max-width: 350px;
            transform: scale(0.9);
            transform-origin: top left;
        }

        .modern-datepicker ::deep .mud-picker-calendar-container {
            max-width: 320px;
        }

        .modern-datepicker ::deep .mud-picker-calendar-header {
            padding: 8px 16px;
        }

        .modern-datepicker ::deep .mud-picker-calendar-content {
            padding: 8px;
        }

        .modern-datepicker ::deep .mud-button-month {
            font-size: 0.875rem;
            padding: 4px 8px;
        }

        .modern-datepicker ::deep .mud-picker-calendar-day {
            width: 32px;
            height: 32px;
            font-size: 0.875rem;
        }

    /* Step Navigation Styling */
    .step-navigation {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 1rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .step-button {
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
        min-width: 140px;
    }

    .next-button, .submit-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

        .next-button:hover, .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

    .previous-button {
        border: 2px solid #6b7280;
        color: #6b7280;
        background: rgba(107, 114, 128, 0.05);
    }

        .previous-button:hover {
            background: rgba(107, 114, 128, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(107, 114, 128, 0.2);
        }

    .delete-button {
        border: 2px solid #ef4444;
        color: #ef4444;
        background: rgba(239, 68, 68, 0.05);
    }

        .delete-button:hover {
            background: rgba(239, 68, 68, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
        }

    /* Section Titles */
    .section-title {
        color: #2c3e50;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    /* Photo Upload Section */
    .photo-upload-section {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .upload-dropzone {
        border: 2px dashed #667eea;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: rgba(102, 126, 234, 0.05);
        cursor: pointer;
        transition: all 0.3s ease;
    }

        .upload-dropzone:hover {
            border-color: #5a67d8;
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

    .upload-icon {
        color: #667eea;
        font-size: 3rem;
    }

    .uploaded-filename {
        color: #667eea;
        font-weight: 500;
    }

    .uploaded-photo-section {
        background: rgba(34, 197, 94, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .photo-status {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .success-chip {
        background: linear-gradient(135deg, #22c55e, #16a34a);
        color: white;
        border-radius: 20px;
        font-weight: 500;
    }

    .remove-photo-btn {
        border-radius: 8px;
        transition: all 0.2s ease;
    }

        .remove-photo-btn:hover {
            background-color: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

    .preview-btn {
        color: #667eea;
        font-weight: 500;
    }

    .image-preview {
        text-align: center;
    }

    .preview-image {
        max-width: 300px;
        max-height: 300px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    /* Action Buttons */
    .action-buttons-section {
        margin-top: 2rem;
    }

    .action-button {
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
        min-width: 140px;
    }

    .primary-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

    .delete-button {
        border: 2px solid #ef4444;
        color: #ef4444;
        background: rgba(239, 68, 68, 0.05);
    }

        .delete-button:hover {
            background: rgba(239, 68, 68, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
        }

    /* Utility Classes */
    .text-muted {
        color: #6b7280 !important;
    }

    /* Responsive Design */
    @@media only screen and (max-width: 1200px) {
        .patient-stepper-section {
            width: 95%;
        }

        #patient-step-content {
            width: 93%;
        }
    }

    @@media only screen and (max-width: 768px) {
        .patient-stepper-section {
            width: 100%;
            margin: 20px auto;
        }

        #patient-step-content {
            width: auto;
            margin: auto 3%;
        }
    }

    @@media (max-width: 768px) {
        .photo-upload-section, .uploaded-photo-section {
            padding: 1rem;
        }

        .page-title {
            font-size: 1.75rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .upload-dropzone {
            padding: 1.5rem;
        }

        .action-button {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .card-content-padding {
            padding: 1rem;
        }

        .step-navigation {
            flex-direction: column;
            gap: 0.5rem;
        }

        .step-button {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }

    @@media (max-width: 480px) {
        .patient-stepper-section {
            margin: 10px auto;
        }

        #patient-step-content {
            margin: auto 2%;
        }

        .step-button {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            min-width: 120px;
        }

        .patient-management-container .mud-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
    }



    *:focus-visible {
        outline: none !important;
    }
</style>
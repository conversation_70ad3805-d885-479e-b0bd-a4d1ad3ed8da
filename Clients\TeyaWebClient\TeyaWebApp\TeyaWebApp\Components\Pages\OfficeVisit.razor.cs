﻿using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models.TermStore;
using Syncfusion.Blazor.Navigations.Internal;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class OfficeVisit : ComponentBase
    {
        private List<OfficeVisitModel> OfficeVisits = new List<OfficeVisitModel>();
        private List<OfficeVisitModel> FilteredOfficeVisits = new List<OfficeVisitModel>();
        private DateTime? _selectedFilterDate;

        private DateTime? SelectedFilterDate
        {
            get => _selectedFilterDate;
            set
            {
                if (_selectedFilterDate != value)
                {
                    _selectedFilterDate = value;
                    FilterVisitsByDate();
                }
            }
        }

        [Inject] private IOfficeVisitService _visitService { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IChartService _chartService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [CascadingParameter] private Admin AdminLayout { get; set; }
        [Inject] private ActiveUser User { get; set; }

        private Guid userid;
        private Guid organizationId { get; set; }
        private bool Subscription { get; set; }

        /// <summary>
        /// Initializes the component by fetching the list of office visits for the user.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            userid = Guid.TryParse(User?.id, out Guid parsedId) ? parsedId : Guid.Empty;
            organizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == "Enterprise";
            OfficeVisits = await _visitService.GetPatientListByIdAsync(userid, organizationId, Subscription, User.role);

            // Initialize filtered list with all visits
            FilteredOfficeVisits = OfficeVisits != null
                ? new List<OfficeVisitModel>(OfficeVisits)
                : new List<OfficeVisitModel>();
        }

        /// <summary>
        /// Filters the office visits based on the selected date
        /// </summary>
        private void FilterVisitsByDate()
        {
            if (SelectedFilterDate.HasValue)
            {
                // Filter visits by the selected date - show only appointments for selected date
                FilteredOfficeVisits = OfficeVisits.Where(visit =>
                    visit.AppointmentDate.HasValue &&
                    visit.AppointmentDate.Value.Date == SelectedFilterDate.Value.Date)
                    .ToList();

                // Debug logging - remove in production
                Console.WriteLine($"Filter applied for date: {SelectedFilterDate.Value.Date}");
                Console.WriteLine($"Total visits: {OfficeVisits.Count}, Filtered visits: {FilteredOfficeVisits.Count}");
            }
            else
            {
                // Show all visits when no date is selected (cleared)
                FilteredOfficeVisits = new List<OfficeVisitModel>(OfficeVisits);
                Console.WriteLine("Filter cleared - showing all visits");
            }

            // Trigger UI update
            InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Redirects the user to the patient chart page.
        /// </summary>
        /// <param name="visitId">patient id - The ID of the office visit.</param>
        private async Task RedirectToChart(Guid visitId, String VisitStatus, String VisitType)
        {
            AdminLayout?.DrawerClose();
            await InvokeAsync(StateHasChanged);
            var patientData = await _chartService.GetPatientByIdAsync(visitId, organizationId, Subscription);
            PatientService.PatientData = patientData;
            PatientService.VisitStatus = VisitStatus;
            PatientService.VisitType = VisitType;
            NavigationManager.NavigateTo("/chart");
        }
    }
}
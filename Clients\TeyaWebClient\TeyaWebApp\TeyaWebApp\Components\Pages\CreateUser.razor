﻿@page "/createmember"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "CreatememberAccessPolicy")]
@using BusinessLayer.Services
@using System.ComponentModel.DataAnnotations
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Model
@using TeyaWebApp.ViewModel
@using TeyaWebApp.Components.Layout
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@layout Admin
@inject RazorComponentRenderer RazorRenderer
@using TeyaUIModels.Model
@inject InviteMailParametersService inviteMailParametersService
@using System.Text.RegularExpressions
@using Microsoft.Extensions.Logging.Abstractions
@using Microsoft.AspNetCore.Components.Web
@inject ActiveUser ActiveUser
<MudPaper Style="padding: 16px; width: 100%;">
    <MudCard Style="max-width: 600px; margin: auto;padding: 16px;">
            <MudForm @ref="form" @bind-IsValid="isFormValid">
                <MudGrid Spacing="3">
                    <MudItem xs="6">
                        <MudTextField T="string" Label="Username" HelperText="Choose a valid username" @ref="usernameField"
                                      Validation="@(new Func<string, IEnumerable<string>>(UsernameValidation))"
                                      Disabled="_isRoleNameDisabled"
                                      Required="true" RequiredError="@Localizer["Username is required!"]"
                                      @bind-Value="member.UserName" />
                    </MudItem>

                    <MudItem xs="6">
                        <MudTextField T="string" Label="@Localizer["Email"]" Required="true" @bind-Value="member.Email"
                                      RequiredError="@Localizer["Email is Required"]"  
                                      Disabled="_isRoleNameDisabled"  
                                      OnBlur="ValidateEmail"
                                      ErrorText="@emailValidationError"
                                      Error="@(!string.IsNullOrWhiteSpace(emailValidationError))" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["First Name"]" @bind-Value="member.FirstName"
                                      Required="true" RequiredError="@Localizer["First Name is required!"]"
                                      Validation="@ValidateAlphabetic" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["Middle Name"]" @bind-Value="member.MiddleName"
                                      Validation="@ValidateAlphabetic"/>
                    </MudItem>

                    <MudItem xs="4">
                    <MudTextField Label="@Localizer["Last Name"]" @bind-Value="member.LastName" Required="true" Validation="@ValidateAlphabetic" RequiredError="@Localizer["Last Name is required!"]" />
                    </MudItem>

                    <MudItem xs="4">
                    <MudAutocomplete T="string"
                                     Required="true"
                                     Label="@Localizer["Country"]"
                                     Value="@member.Country"
                                     ValueChanged="@(value => member.Country = value)"
                                     SearchFunc="SearchCountries"
                                     Clearable="true"
                                     Placeholder="Search Country" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["Federal Tax Id"]" @bind-Value="member.FederalTaxId"
                                      Validation="@(new Func<string, IEnumerable<string>>(TaxIdValidation))" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["UPIN"]" @bind-Value="member.UPIN"
                                      Validation="@(new Func<string, IEnumerable<string>>(UPINValidation))" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["DEA Number"]"
                                      @bind-Value="member.DEANumber"
                                      Validation="@(new Func<string, IEnumerable<string>>(DEAValidation))" />
                    </MudItem>
                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["Job Description"]" @bind-Value="member.JobDescription"
                                      Validation="@(new Func<string, IEnumerable<string>>(JobDescriptionValidation))" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@($"{Localizer["Supervisor"]} (Optional)")" @bind-Value="member.Supervisor" />
                    </MudItem>


                    <MudItem xs="4">
                        <MudTextField Label="@($"{Localizer["Taxonomy"]} (Optional)")" @bind-Value="member.Taxonomy" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@($"{Localizer["NewCrop eRx Role"]} (Optional)")" @bind-Value="member.NewCropERxRole" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField Label="@($"{Localizer["Additional Info"]} (Optional)")" @bind-Value="member.AdditionalInfo"
                                      Validation="@(new Func<string, IEnumerable<string>>(AdditionalInfoValidation))" />
                    </MudItem>


                    <MudItem xs="4">
                        <MudTextField Label="@Localizer["Default Billing Facility"]" @bind-Value="member.DefaultBillingFacility" />
                    </MudItem>

                    <MudItem xs="4">
                        <MudSelect T="string" Label="@Localizer["SelectProviderType"]" AnchorOrigin="Origin.BottomCenter" @bind-Value="member.ProviderType">
                            <MudSelectItem T="string" Value="@Localizer["AttendingPhysician"]">@Localizer["AttendingPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["AudiologicalPhysician"]">@Localizer["AudiologicalPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["ChestPhysician"]">@Localizer["ChestPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["CommunityHealthPhysician"]">@Localizer["CommunityHealthPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["ConsultantPhysician"]">@Localizer["ConsultantPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["GeneralPhysician"]">@Localizer["GeneralPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["GenitourinaryMedicinePhysician"]">@Localizer["GenitourinaryMedicinePhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["OccupationalPhysician"]">@Localizer["OccupationalPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["PalliativeCarePhysician"]">@Localizer["PalliativeCarePhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["Physician"]">@Localizer["Physician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["PublicHealthPhysician"]">@Localizer["PublicHealthPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["RehabilitationPhysician"]">@Localizer["RehabilitationPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["ResidentPhysician"]">@Localizer["ResidentPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["SpecializedPhysician"]">@Localizer["SpecializedPhysician"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["ThoracicPhysician"]">@Localizer["ThoracicPhysician"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>

                    <MudItem xs="4">

                        <MudSelect T="string" Label="@Localizer["PatientMenuRole"]" AnchorOrigin="Origin.BottomCenter" @bind-Value="member.PatientMenuRole">
                            <MudSelectItem T="string" Value="@Localizer["Standard"]">@Localizer["Standard"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@Localizer["Custom"]">@Localizer["Custom"]</MudSelectItem>
                        </MudSelect>

                    </MudItem>

                    <MudItem xs="4">
                        <MudTextField T="string" Value="_selectedOrganization" ReadOnly="true" Disabled="_isRoleNameDisabled" Label="Organization" />
                    </MudItem>


                    <MudItem xs="4">
                        <MudAutocomplete T="string" Label="@Localizer["Role"]" @bind-Value="member.AccessControl"
                                         Required="true" RequiredError="@Localizer["Role Selection is required!"]"
                                         SearchFunc="SearchRoles" ResetValueOnEmpty="true" Clearable="true"
                                         Disabled="_isRoleNameDisabled" />
                    </MudItem>

                </MudGrid>
            </MudForm>

           

            <!-- Replace your existing button section with this: -->

            <MudGrid Spacing="3">
                <MudItem xs="12">
                    <div style="display: flex; justify-content: flex-end; margin-top: 16px; margin-right:16px; gap: 8px;">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Secondary"
                                   OnClick="Cancel"
                                   Style="min-width: 100px; height: 36px; padding: 2px 16px; font-size: 0.8rem;">
                            @Localizer["Cancel"]
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   OnClick="Save"
                                   Style="min-width: 100px; height: 36px; padding: 2px 16px; font-size: 0.8rem;">
                            @Localizer["Add"]
                        </MudButton>
                    </div>
                </MudItem>
            </MudGrid>
    </MudCard>
</MudPaper>

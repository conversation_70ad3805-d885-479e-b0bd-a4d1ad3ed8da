﻿@page "/encounters"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IProgressNotesService ProgressNotesService
@inject HttpClient Http
@using Markdig

<GenericCard>
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4 my-gen-card">
        @if (records == null)
        {
            <MudAlert Severity="Severity.Info">@Localizer["Loading..."]</MudAlert>
        }
         else if (!records.Any())
        {
           
            <MudAlert Severity="Severity.Info">@Localizer["NoRecordsFound"]</MudAlert>
        }
        else if (selectedRecord == null)
        {

            <div class="filter-container mb-3">
                <MudPaper Class="pa-2" Elevation="1">
                    <div class="d-flex flex-wrap align-items-center gap-2">
                        <MudTextField @bind-Value="patientFilter"
                                      Placeholder="Search By Patient Name . . . "
                                      Label="Patient"
                                      Variant="Variant.Outlined"
                                      Class="me-2"
                                      Immediate="true"
                                      Margin="Margin.Dense"
                                     />

                        <MudTextField @bind-Value="providerFilter"
                                      Placeholder="Search By Provider Name . . . "
                                      Label="Provider"
                                      Variant="Variant.Outlined"
                                      Class="me-2"
                                      Immediate="true"
                                      Margin="Margin.Dense"
                                     />
                     
                       
                        <MudDatePicker @bind-Date="selectedDate"
                                           Placeholder="Search by Date. . ."
                                           Label="Date"
                                           Variant="Variant.Outlined"
                                           Margin="Margin.Dense"
                                           Class="me-2"
                                           Clearable="true"/>
                         
                        
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   Size="Size.Small"
                                   Style="height:35px;"
                                   OnClick="ResetFilters">
                            Reset
                        </MudButton>
                    </div>
                </MudPaper>
            </div>

            <div class="grid-container">
                <SfGrid @ref="Grid" DataSource="@filteredGridData" AllowPaging="true" GridLines="GridLine.Both">
                    <GridEvents TValue="GridRecord" RowSelected="@OnRowSelected"></GridEvents>

                    <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="false"></GridEditSettings>
                    <GridColumns>

                        <GridColumn Field=@nameof(GridRecord.DateTime) HeaderText="Date" Width="100" AllowEditing="false" TextAlign="TextAlign.Center"
                                    Format="MMM dd, yyyy">
                        </GridColumn>

                        <GridColumn Field=@nameof(GridRecord.Physician) HeaderText="Provider" Width="110" AllowEditing="false" TextAlign="TextAlign.Center">
                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.PatientName) HeaderText="Patient" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.ChiefComplaint) HeaderText="Chief Complaint" AllowEditing="false" TextAlign="TextAlign.Left">
                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.Status) HeaderText="Status" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.Billing) HeaderText="Billing" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        }
    </MudContainer>
           
        
</GenericCard>



<style>

    .my-gen-card {
        padding: 0px !important;
    }

    .mud-card-header {
        padding: 0 !important;
    }

    .mud-card-content {
        padding: 0px !important;
        padding-left: 2% !important;
    }

    /* Grid Rows */
    .e-row {
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
    }

        .e-row:hover {
            background-color: #f5faff;
            cursor: pointer;
        }

    .e-altrow {
        background-color: #fcfcfc;

    }
</style>
